<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能图表生成器 - AI驱动的数据可视化</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5/dist/echarts.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/d3@7"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4"></script>
    <script src="https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
            background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
            min-height: 100vh;
            color: #374151;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 24px;
        }

        .header {
            text-align: center;
            margin-bottom: 32px;
            color: #1f2937;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 2.2rem;
            font-weight: 600;
            margin-bottom: 8px;
            color: #111827;
            letter-spacing: -0.025em;
        }

        .header p {
            font-size: 1rem;
            color: #6b7280;
            margin: 0;
            font-weight: 400;
        }

        .main-content {
            display: grid;
            grid-template-columns: 400px 1fr;
            gap: 24px;
            height: calc(100vh - 200px);
        }

        .control-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(229, 231, 235, 0.8);
            overflow-y: auto;
        }

        .chart-area {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(229, 231, 235, 0.8);
            position: relative;
        }

        .section {
            margin-bottom: 25px;
        }

        .section-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 16px;
            color: #1f2937;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .section-title::before {
            content: '';
            width: 3px;
            height: 16px;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            border-radius: 2px;
        }

        .input-group {
            margin-bottom: 16px;
        }

        .input-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .input-field {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            background: #fefefe;
        }

        .input-field:focus {
            outline: none;
            border-color: #6366f1;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            background: #ffffff;
        }

        .textarea-field {
            min-height: 120px;
            resize: vertical;
            font-family: 'Monaco', 'Menlo', monospace;
            line-height: 1.5;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            letter-spacing: 0.025em;
        }

        .btn-primary {
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            color: white;
            box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(99, 102, 241, 0.3);
        }

        .btn-secondary {
            background: #f9fafb;
            color: #6b7280;
            border: 1px solid #d1d5db;
        }

        .btn-secondary:hover {
            background: #f3f4f6;
            border-color: #9ca3af;
        }

        .btn-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .chart-container {
            width: 100%;
            height: 500px;
            border: 1px solid #e5e7eb;
            border-radius: 0; /* 直角设计 */
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
            font-size: 1.1rem;
            background: #fefefe;
        }

        .chart-ready {
            border: 1px solid #e5e7eb;
            background: #ffffff;
        }

        .loading {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.95);
            display: none;
            align-items: center;
            justify-content: center;
            border-radius: 0;
            z-index: 1000;
        }

        .spinner {
            width: 36px;
            height: 36px;
            border: 3px solid #f3f4f6;
            border-top: 3px solid #6366f1;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .ai-badge {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .config-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .status-message {
            padding: 12px;
            border-radius: 8px;
            margin-top: 15px;
            font-size: 0.9rem;
        }

        .status-success {
            background: #f0fff4;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }

        .status-error {
            background: #fed7d7;
            color: #742a2a;
            border: 1px solid #feb2b2;
        }

        .status-warning {
            background: #fffbeb;
            color: #92400e;
            border: 1px solid #fcd34d;
        }

        .status-info {
            background: #eff6ff;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }

        /* 改进的状态消息样式 */
        .status-message {
            padding: 12px 16px;
            border-radius: 8px;
            margin-top: 15px;
            font-size: 0.9rem;
            display: flex;
            align-items: flex-start;
            gap: 10px;
            animation: slideIn 0.3s ease-out;
        }

        .status-icon {
            font-size: 1.2rem;
            flex-shrink: 0;
            margin-top: 1px;
        }

        .status-content {
            flex: 1;
        }

        .status-title {
            font-weight: 600;
            margin-bottom: 4px;
        }

        .status-description {
            font-size: 0.85rem;
            opacity: 0.9;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 重试按钮样式 */
        .retry-btn {
            background: none;
            border: none;
            color: inherit;
            text-decoration: underline;
            cursor: pointer;
            font-size: 0.85rem;
            margin-top: 8px;
            padding: 0;
        }

        .retry-btn:hover {
            opacity: 0.8;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f1f5f9;
        }

        .chart-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2d3748;
        }

        .chart-actions {
            display: flex;
            gap: 8px;
        }

        .btn-icon {
            padding: 8px;
            min-width: auto;
            border-radius: 6px;
            font-size: 0.9rem;
        }

        .btn-icon:hover {
            transform: translateY(-1px);
        }

        .zoom-controls {
            position: absolute;
            top: 15px;
            right: 15px;
            display: flex;
            flex-direction: column;
            gap: 5px;
            z-index: 100;
        }

        .zoom-btn {
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.9);
            color: #4a5568;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.2s;
        }

        .zoom-btn:hover {
            background: white;
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .chart-container-wrapper {
            position: relative;
            width: 100%;
            height: 500px;
            overflow: hidden;
            border-radius: 8px;
        }

        /* 示例菜单样式 */
        .example-item:hover {
            background: #f8fafc !important;
        }

        #exampleMenu {
            animation: fadeIn 0.2s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-5px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 系统设置对话框样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 2000;
            backdrop-filter: blur(4px);
        }

        .modal {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-20px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-header {
            padding: 20px 24px 16px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .modal-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2328;
            margin: 0;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: #6e7781;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s;
        }

        .modal-close:hover {
            background: #f6f8fa;
            color: #1f2328;
        }

        .modal-body {
            padding: 20px 24px;
        }

        .modal-footer {
            padding: 16px 24px 20px;
            border-top: 1px solid #e2e8f0;
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }

        .settings-section {
            margin-bottom: 24px;
        }

        .settings-section:last-child {
            margin-bottom: 0;
        }

        .settings-section-title {
            font-size: 1rem;
            font-weight: 600;
            color: #1f2328;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .settings-icon {
            font-size: 1.1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                <div>
                    <h1>🎯 智能图表生成器</h1>
                    <p>用自然语言描述需求，AI自动生成专业图表</p>
                </div>
                <button id="settingsBtn" class="btn btn-secondary" style="margin: 0;">
                    ⚙️ 系统设置
                </button>
            </div>
        </div>
        
        <div id="statusMessage"></div>

        <div class="main-content">
            <div class="control-panel">
                
                <div class="section">
                    <div class="section-title">
                        <span class="ai-badge">AI</span>
                        智能分析
                    </div>
                    
                    <div class="input-group">
                        <label class="input-label">描述你的数据可视化需求</label>
                        <textarea
                            id="userInput"
                            class="input-field textarea-field"
                            placeholder="例如：显示2023年各季度销售数据的柱状图，Q1: 120万，Q2: 150万，Q3: 180万，Q4: 200万

💡 提示：
• 可以直接描述数据和图表类型
• 支持中文数字和单位（万、千、%等）
• 按 Ctrl+Enter 快速生成图表
• 选择具体图表类型可获得更精确的结果"
                        ></textarea>
                    </div>

                    <div class="btn-group">
                        <button id="generateBtn" class="btn btn-primary">
                            ✨ AI生成图表
                        </button>
                        <div style="position: relative; display: inline-block;">
                            <button id="exampleBtn" class="btn btn-secondary">
                                📝 图表数据示例 ▼
                            </button>
                            <div id="exampleMenu" style="display: none; position: absolute; top: 100%; left: 0; background: white; border: 1px solid #e2e8f0; border-radius: 6px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 1000; min-width: 200px;">
                                <button class="example-item" data-example="bar" style="width: 100%; padding: 8px 12px; border: none; background: none; text-align: left; cursor: pointer; font-size: 0.9rem;">📊 柱状图示例</button>
                                <button class="example-item" data-example="pie" style="width: 100%; padding: 8px 12px; border: none; background: none; text-align: left; cursor: pointer; font-size: 0.9rem;">🥧 饼图示例</button>
                                <button class="example-item" data-example="line" style="width: 100%; padding: 8px 12px; border: none; background: none; text-align: left; cursor: pointer; font-size: 0.9rem;">📈 折线图示例</button>
                                <button class="example-item" data-example="org-chart" style="width: 100%; padding: 8px 12px; border: none; background: none; text-align: left; cursor: pointer; font-size: 0.9rem;">🏢 架构图示例</button>
                                <button class="example-item" data-example="wordcloud" style="width: 100%; padding: 8px 12px; border: none; background: none; text-align: left; cursor: pointer; font-size: 0.9rem;">☁️ 词频图示例</button>
                                <button class="example-item" data-example="timeline" style="width: 100%; padding: 8px 12px; border: none; background: none; text-align: left; cursor: pointer; font-size: 0.9rem;">⏰ 时间线示例</button>
                                <button class="example-item" data-example="table" style="width: 100%; padding: 8px 12px; border: none; background: none; text-align: left; cursor: pointer; font-size: 0.9rem;">📋 表格示例</button>
                                <button class="example-item" data-example="test" style="width: 100%; padding: 8px 12px; border: none; background: none; text-align: left; cursor: pointer; font-size: 0.9rem; border-top: 1px solid #f1f5f9;">🧪 直接测试</button>
                            </div>
                        </div>
                    </div>
                </div>



                <div class="section">
                    <div class="section-title">图表配置</div>

                    <div class="input-group">
                        <label class="input-label">图表框架</label>
                        <select id="framework" class="input-field">
                            <option value="auto">自动选择</option>
                            <option value="echarts">ECharts</option>
                            <option value="d3">D3.js</option>
                            <option value="chartjs">Chart.js</option>
                        </select>
                    </div>

                    <div class="input-group">
                        <label class="input-label">图表类型</label>
                        <select id="chartType" class="input-field">
                            <option value="auto">AI自动判断</option>
                            <optgroup label="基础图表">
                                <option value="bar">柱状图</option>
                                <option value="line">折线图</option>
                                <option value="pie">饼图</option>
                                <option value="scatter">散点图</option>
                                <option value="bubble">气泡图</option>
                            </optgroup>
                            <optgroup label="高级图表">
                                <option value="radar">雷达图</option>
                                <option value="boxplot">箱线图</option>
                                <option value="heatmap">热力图</option>
                                <option value="calendar-heatmap">日历热力图</option>
                            </optgroup>
                            <optgroup label="关系图表">
                                <option value="force">力导向图</option>
                                <option value="sankey">桑基图</option>
                                <option value="tree">树状图</option>
                                <option value="chord">弦图</option>
                            </optgroup>
                            <optgroup label="地理图表">
                                <option value="choropleth">等值线图</option>
                                <option value="map-scatter">地图散点图</option>
                                <option value="map-lines">地图连线图</option>
                            </optgroup>
                            <optgroup label="其他">
                                <option value="gantt">甘特图</option>
                                <option value="voronoi">Voronoi图</option>
                                <option value="org-chart">架构图</option>
                                <option value="wordcloud">词频图</option>
                                <option value="timeline">时间发展趋势图</option>
                                <option value="table">表格</option>
                            </optgroup>
                        </select>
                    </div>

                    <div id="chartTypeHint" class="input-group" style="display: none;">
                        <div class="input-label" style="color: #667eea; font-weight: 600;">💡 数据格式提示</div>
                        <div id="chartTypeHintText" style="font-size: 0.85rem; color: #4a5568; background: #f8fafc; padding: 10px; border-radius: 6px; border-left: 3px solid #667eea;">
                        </div>
                    </div>
                </div>

                <div class="section">
                    <div class="section-title">生成的数据</div>
                    <textarea 
                        id="generatedData" 
                        class="input-field textarea-field" 
                        placeholder="AI生成的图表数据将显示在这里..."
                        readonly
                    ></textarea>
                    
                    <div class="btn-group" style="margin-top: 10px;">
                        <button id="renderBtn" class="btn btn-primary">
                            🎨 渲染图表
                        </button>
                        <button id="copyBtn" class="btn btn-secondary">
                            📋 复制数据
                        </button>
                    </div>
                </div>

                
            </div>

            <div class="chart-area">
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                </div>

                <div class="chart-header">
                    <div class="chart-title" id="chartTitle">图表预览</div>
                    <div class="chart-actions">
                        <button id="downloadPngBtn" class="btn btn-icon btn-secondary" title="下载PNG">
                            📷
                        </button>
                        <button id="downloadSvgBtn" class="btn btn-icon btn-secondary" title="下载SVG">
                            🎨
                        </button>
                        <button id="copyChartBtn" class="btn btn-icon btn-secondary" title="复制图表">
                            📋
                        </button>
                        <button id="fullscreenBtn" class="btn btn-icon btn-secondary" title="全屏显示">
                            🔍
                        </button>
                        <button id="refreshBtn" class="btn btn-icon btn-secondary" title="刷新图表">
                            🔄
                        </button>
                    </div>
                </div>

                <div class="chart-container-wrapper">
                    <div class="zoom-controls" id="zoomControls" style="display: none;">
                        <button class="zoom-btn" id="zoomInBtn" title="放大">+</button>
                        <button class="zoom-btn" id="zoomOutBtn" title="缩小">-</button>
                        <button class="zoom-btn" id="zoomResetBtn" title="重置">⌂</button>
                    </div>

                    <div id="chartContainer" class="chart-container">
                        <div>
                            <div style="font-size: 3rem; margin-bottom: 10px;">📊</div>
                            <div>在左侧描述你的需求，AI将为你生成图表</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 系统设置对话框 -->
    <div id="settingsModal" class="modal-overlay">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">⚙️ 系统设置</h3>
                <button id="closeSettingsBtn" class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="settings-section">
                    <div class="settings-section-title">
                        <span class="settings-icon">🔗</span>
                        API配置
                    </div>

                    <div class="input-group">
                        <label class="input-label">API URL</label>
                        <input
                            type="text"
                            id="apiUrl"
                            class="input-field"
                            placeholder="https://openrouter.ai/api/v1/chat/completions"
                            value="https://openrouter.ai/api/v1/chat/completions"
                        >
                    </div>

                    <div class="input-group">
                        <label class="input-label">API Key</label>
                        <input
                            type="password"
                            id="apiKey"
                            class="input-field"
                            placeholder="sk-or-v1-..."
                            value="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"
                        >
                    </div>

                    <div class="input-group">
                        <label class="input-label">AI模型</label>
                        <select id="model" class="input-field">
                            <option value="deepseek/deepseek-chat-v3-0324:free">DeepSeek Chat (免费)</option>
                            <option value="meta-llama/llama-3.1-8b-instruct:free">Llama 3.1 8B (免费)</option>
                            <option value="microsoft/phi-3-mini-128k-instruct:free">Phi-3 Mini (免费)</option>
                            <option value="openai/gpt-3.5-turbo">GPT-3.5 Turbo</option>
                            <option value="openai/gpt-4">GPT-4</option>
                            <option value="anthropic/claude-3-haiku">Claude 3 Haiku</option>
                        </select>
                    </div>
                </div>

                <div class="settings-section">
                    <div class="settings-section-title">
                        <span class="settings-icon">🛠️</span>
                        系统选项
                    </div>

                    <div class="input-group">
                        <label class="input" style="display: flex; align-items: center; gap: 8px;">
                            <input id="debugMode" type="checkbox" checked />
                            <span>🐛 调试模式（控制台详细日志）</span>
                        </label>
                    </div>

                    <div class="input-group">
                        <label class="input" style="display: flex; align-items: center; gap: 8px;">
                            <input id="autoRender" type="checkbox" checked />
                            <span>🎨 自动渲染图表</span>
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button id="resetSettingsBtn" class="btn btn-secondary">
                    🔄 重置默认
                </button>
                <button id="saveSettingsBtn" class="btn btn-primary">
                    💾 保存设置
                </button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let chartInstance = null;
        let currentZoom = 1;
        let currentChartData = null;
        let debugMode = true; // 调试模式开关

        // 调试日志函数
        function debugLog(message, ...args) {
            if (debugMode) {
                console.log(message, ...args);
            }
        }

        function debugError(message, ...args) {
            if (debugMode) {
                console.error(message, ...args);
            }
        }

        // 调试信息汇总函数
        function logApiError(response, errorBody, apiUrl, model) {
            if (!debugMode) return;
            console.group('🚨 API错误详情');
            console.error('状态码:', response.status);
            console.error('状态文本:', response.statusText);
            console.error('请求URL:', apiUrl);
            console.error('请求模型:', model);
            console.error('响应头:', Object.fromEntries(response.headers.entries()));
            console.error('响应体:', errorBody);
            console.groupEnd();
        }

        function logApiResponse(result, response) {
            if (!debugMode) return;
            console.group('✅ AI响应详情');
            console.log('响应状态:', response.status);
            console.log('完整响应:', result);
            console.log('AI内容:', result.choices?.[0]?.message?.content);
            console.groupEnd();
        }

        function logJsonParsing(content, jsonData) {
            if (!debugMode) return;
            console.group('🔍 JSON解析过程');
            console.log('原始内容:', content);
            console.log('解析结果:', jsonData);
            console.log('数据验证:', {
                hasType: !!jsonData.type,
                hasData: !!jsonData.data,
                type: jsonData.type
            });
            console.groupEnd();
        }

        function logRenderProcess(data, framework, error = null) {
            if (!debugMode) return;
            if (error) {
                console.group('❌ 渲染错误');
                console.error('错误信息:', error.message);
                console.error('错误堆栈:', error.stack);
                console.error('渲染数据:', data);
                console.error('使用框架:', framework);
                console.groupEnd();
            } else {
                console.group('🎨 渲染过程');
                console.log('图表类型:', data.type);
                console.log('使用框架:', framework);
                console.log('渲染数据:', data);
                console.groupEnd();
            }
        }

        // DOM元素
        const elements = {
            userInput: document.getElementById('userInput'),
            apiUrl: document.getElementById('apiUrl'),
            apiKey: document.getElementById('apiKey'),
            model: document.getElementById('model'),
            framework: document.getElementById('framework'),
            chartType: document.getElementById('chartType'),
            debugMode: document.getElementById('debugMode'),
            autoRender: document.getElementById('autoRender'),
            chartTypeHint: document.getElementById('chartTypeHint'),
            chartTypeHintText: document.getElementById('chartTypeHintText'),
            generatedData: document.getElementById('generatedData'),
            chartContainer: document.getElementById('chartContainer'),
            chartTitle: document.getElementById('chartTitle'),
            loading: document.getElementById('loading'),
            statusMessage: document.getElementById('statusMessage'),
            zoomControls: document.getElementById('zoomControls'),
            generateBtn: document.getElementById('generateBtn'),
            renderBtn: document.getElementById('renderBtn'),
            exampleBtn: document.getElementById('exampleBtn'),
            exampleMenu: document.getElementById('exampleMenu'),
            copyBtn: document.getElementById('copyBtn'),
            // 设置对话框相关
            settingsBtn: document.getElementById('settingsBtn'),
            settingsModal: document.getElementById('settingsModal'),
            closeSettingsBtn: document.getElementById('closeSettingsBtn'),
            saveSettingsBtn: document.getElementById('saveSettingsBtn'),
            resetSettingsBtn: document.getElementById('resetSettingsBtn'),
            downloadPngBtn: document.getElementById('downloadPngBtn'),
            downloadSvgBtn: document.getElementById('downloadSvgBtn'),
            copyChartBtn: document.getElementById('copyChartBtn'),
            fullscreenBtn: document.getElementById('fullscreenBtn'),
            refreshBtn: document.getElementById('refreshBtn'),
            zoomInBtn: document.getElementById('zoomInBtn'),
            zoomOutBtn: document.getElementById('zoomOutBtn'),
            zoomResetBtn: document.getElementById('zoomResetBtn')
        };

        // 加载保存的配置
        function loadConfig() {
            const config = JSON.parse(localStorage.getItem('chartGeneratorConfig') || '{}');
            if (config.apiUrl) elements.apiUrl.value = config.apiUrl;
            if (config.apiKey) elements.apiKey.value = config.apiKey;
            if (config.model) elements.model.value = config.model;
            if (config.framework) elements.framework.value = config.framework;
            if (config.chartType) elements.chartType.value = config.chartType;
            if (typeof config.debugMode === 'boolean') {
                elements.debugMode.checked = config.debugMode;
                debugMode = config.debugMode;
            }
            if (typeof config.autoRender === 'boolean') {
                elements.autoRender.checked = config.autoRender;
            }
        }

        // 保存配置
        function saveConfig() {
            const config = {
                apiUrl: elements.apiUrl.value.trim(),
                apiKey: elements.apiKey.value.trim(),
                model: elements.model.value,
                framework: elements.framework.value,
                chartType: elements.chartType.value,
                debugMode: elements.debugMode.checked,
                autoRender: elements.autoRender.checked
            };

            // 验证配置
            if (!config.apiUrl) {
                showStatus('API URL不能为空', 'warning');
                return;
            }

            if (!config.apiKey) {
                showStatus('API Key不能为空', 'warning');
                return;
            }

            if (!config.apiUrl.startsWith('http')) {
                showStatus('API URL格式不正确', 'warning', {
                    description: '请输入完整的HTTP/HTTPS地址'
                });
                return;
            }

            localStorage.setItem('chartGeneratorConfig', JSON.stringify(config));
            showStatus('配置已保存', 'success', {
                description: '您的API配置已安全保存到本地浏览器'
            });
        }

        // 验证API配置
        function validateApiConfig() {
            const apiUrl = elements.apiUrl.value.trim();
            const apiKey = elements.apiKey.value.trim();

            if (!apiUrl || !apiKey) {
                return false;
            }

            if (!apiUrl.startsWith('http')) {
                return false;
            }

            if (apiKey.length < 10) {
                return false;
            }

            return true;
        }

        // 显示状态消息
        function showStatus(message, type = 'success', options = {}) {
            const {
                title = '',
                description = '',
                duration = 5000,
                showRetry = false,
                retryCallback = null
            } = options;

            const icons = {
                success: '✅',
                error: '❌',
                warning: '⚠️',
                info: 'ℹ️'
            };

            const titles = {
                success: title || '操作成功',
                error: title || '操作失败',
                warning: title || '注意',
                info: title || '提示'
            };

            let retryButton = '';
            if (showRetry && retryCallback) {
                retryButton = `<button class="retry-btn" onclick="(${retryCallback.toString()})()">点击重试</button>`;
            }

            const statusHtml = `
              
            <div class="status-message status-${type}">
                    <div class="status-icon">${icons[type]}</div>
                    <div class="status-content">
                        <div class="status-title">${titles[type]}</div>
                        <div class="status-description">
                            ${message}
                            ${description ? `<br><small>${description}</small>` : ''}
                            ${retryButton}
                        </div>
                    </div>
                </div>
                <br>
            `;

            elements.statusMessage.innerHTML = statusHtml;

            if (duration > 0) {
                setTimeout(() => {
                    elements.statusMessage.innerHTML = '';
                }, duration);
            }
        }

        // 显示/隐藏加载状态
        function setLoading(show) {
            elements.loading.style.display = show ? 'flex' : 'none';
            elements.generateBtn.disabled = show;

            if (show) {
                elements.generateBtn.innerHTML = '🔄 生成中...';
            } else {
                elements.generateBtn.innerHTML = '✨ AI生成图表';
            }
        }

        // API错误处理
        function handleApiError(error, response = null) {
            console.error('API Error:', error, response);

            let errorMessage = '';
            let errorDescription = '';
            let showRetry = true;

            if (response) {
                switch (response.status) {
                    case 429:
                        errorMessage = 'API调用频率超限';
                        errorDescription = '请稍等片刻再试，或检查您的API配额是否充足。通常需要等待1-2分钟。';
                        break;
                    case 401:
                        errorMessage = 'API密钥无效';
                        errorDescription = '请检查您的API Key是否正确，或者密钥是否已过期。';
                        showRetry = false;
                        break;
                    case 403:
                        errorMessage = 'API访问被拒绝';
                        errorDescription = '您的API密钥可能没有访问权限，请检查密钥权限设置。';
                        showRetry = false;
                        break;
                    case 500:
                        errorMessage = 'API服务器错误';
                        errorDescription = 'OpenRouter服务器出现问题，请稍后重试。';
                        break;
                    case 502:
                    case 503:
                    case 504:
                        errorMessage = 'API服务暂时不可用';
                        errorDescription = '服务器正在维护或负载过高，请稍后重试。';
                        break;
                    default:
                        errorMessage = `API请求失败 (${response.status})`;
                        errorDescription = '请检查网络连接和API配置，或稍后重试。';
                }
            } else if (error.message.includes('Failed to fetch')) {
                errorMessage = '网络连接失败';
                errorDescription = '请检查您的网络连接，或者API地址是否正确。';
            } else if (error.message.includes('JSON')) {
                errorMessage = 'AI返回数据格式错误';
                errorDescription = '模型返回的数据无法解析，请重试或更换模型。';
            } else {
                errorMessage = '未知错误';
                errorDescription = error.message || '请重试或联系技术支持。';
            }

            showStatus(errorMessage, 'error', {
                description: errorDescription,
                duration: 8000,
                showRetry: showRetry,
                retryCallback: showRetry ? generateChart : null
            });
        }

        // 网络连接检测
        async function checkNetworkConnection() {
            try {
                const response = await fetch('https://httpbin.org/get', {
                    method: 'GET',
                    mode: 'cors',
                    cache: 'no-cache'
                });
                return response.ok;
            } catch (error) {
                return false;
            }
        }

        // AI分析生成图表数据（带重试机制）
        async function generateChart(retryCount = 0) {
            const userInput = elements.userInput.value.trim();
            const apiUrl = elements.apiUrl.value.trim();
            const apiKey = elements.apiKey.value.trim();
            const model = elements.model.value;
            const preferredType = elements.chartType.value;
            const autoRender = elements.autoRender.checked;
            const maxRetries = 2;

            if (!userInput) {
                showStatus('请输入你的数据可视化需求', 'warning', {
                    description: '在上方文本框中描述你想要创建的图表类型和数据内容。'
                });
                elements.userInput.focus();
                return;
            }

            if (!validateApiConfig()) {
                showStatus('请完善API配置', 'warning', {
                    description: '请检查API URL和API Key是否正确填写。API Key长度应至少10个字符。'
                });
                elements.apiKey.focus();
                return;
            }

            // 检查网络连接（仅在首次尝试时）
            if (retryCount === 0) {
                const isOnline = await checkNetworkConnection();
                if (!isOnline) {
                    showStatus('网络连接异常', 'error', {
                        description: '请检查您的网络连接后重试。',
                        showRetry: true,
                        retryCallback: () => generateChart(0)
                    });
                    return;
                }
            }

            setLoading(true);
            showStatus('正在分析您的需求...', 'info', { duration: 0 });

            try {
                // 如果用户选择了具体的图表类型，强制使用该类型
                const systemPrompt = generateSystemPrompt(preferredType, userInput, true);

                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`,
                        'HTTP-Referer': window.location.origin,
                        'X-Title': 'Chart Generator'
                    },
                    body: JSON.stringify({
                        model: model,
                        messages: [
                            { role: 'system', content: systemPrompt },
                            { role: 'user', content: userInput }
                        ],
                        temperature: 0.3,
                        max_tokens: 2000
                    })
                });

                if (!response.ok) {
                    // 详细记录API错误响应
                    let errorDetails = `API请求失败: ${response.status} ${response.statusText}`;
                    try {
                        const errorBody = await response.text();
                        logApiError(response, errorBody, apiUrl, model);

                        // 尝试解析错误响应
                        try {
                            const errorJson = JSON.parse(errorBody);
                            if (errorJson.error) {
                                errorDetails += `\n错误信息: ${errorJson.error.message || errorJson.error}`;
                            }
                        } catch (parseError) {
                            debugError('无法解析错误响应为JSON:', parseError);
                        }
                    } catch (readError) {
                        debugError('读取错误响应失败:', readError);
                    }

                    handleApiError(new Error(errorDetails), response);
                    return;
                }

                showStatus('正在解析AI响应...', 'info', { duration: 0 });
                const result = await response.json();

                logApiResponse(result, response);

                if (!result.choices || !result.choices[0] || !result.choices[0].message) {
                    debugError('AI响应格式错误 - 响应结构:', result);
                    throw new Error('AI响应格式异常，请重试');
                }

                const content = result.choices[0].message.content;

                if (!content) {
                    debugError('AI返回内容为空');
                    throw new Error('AI返回内容为空，请重试');
                }

                // 提取JSON
                let jsonData = extractJsonFromContent(content);

                // 修复AI返回的不标准格式
                jsonData = fixAiResponseFormat(jsonData);

                logJsonParsing(content, jsonData);

                if (!jsonData.type || !jsonData.data) {
                    debugError('数据结构不完整，缺少字段:', {
                        type: !jsonData.type,
                        data: !jsonData.data
                    });
                    throw new Error('AI返回的数据结构不完整，缺少必要字段');
                }

                elements.generatedData.value = JSON.stringify(jsonData, null, 2);
                showStatus('图表数据生成成功！', 'success', {
                    description: `已生成${getChartTypeName(jsonData.type)}数据${autoRender ? '，正在渲染图表...' : ''}`
                });

                // 根据设置决定是否自动渲染图表
                if (autoRender) {
                    await renderChart(jsonData);
                } else {
                    showStatus('数据生成完成', 'info', {
                        description: '请点击"🎨 渲染图表"按钮来显示图表',
                        duration: 3000
                    });
                }

            } catch (error) {
                // 详细记录错误信息
                console.error('=== 生成图表错误 ===');
                console.error('错误对象:', error);
                console.error('错误消息:', error.message);
                console.error('错误堆栈:', error.stack);
                console.error('错误名称:', error.name);
                console.error('重试次数:', retryCount);
                console.error('最大重试次数:', maxRetries);
                console.error('==================');

                // 自动重试逻辑
                if (retryCount < maxRetries && (
                    error.message.includes('429') ||
                    error.message.includes('502') ||
                    error.message.includes('503') ||
                    error.message.includes('504') ||
                    error.name === 'TypeError'
                )) {
                    const waitTime = Math.pow(2, retryCount) * 1000; // 指数退避
                    console.log(`准备重试，等待时间: ${waitTime}ms`);

                    showStatus(`请求失败，${waitTime/1000}秒后自动重试...`, 'warning', {
                        description: `第${retryCount + 1}次重试，共${maxRetries}次机会`,
                        duration: waitTime
                    });

                    setTimeout(() => {
                        generateChart(retryCount + 1);
                    }, waitTime);
                    return;
                }

                // 最终失败处理
                if (error.name === 'TypeError' && error.message.includes('fetch')) {
                    handleApiError(error);
                } else {
                    showStatus('数据处理失败', 'error', {
                        description: error.message,
                        showRetry: true,
                        retryCallback: () => generateChart(0)
                    });
                }
            } finally {
                setLoading(false);
            }
        }

        // 渲染图表
        async function renderChart(data = null) {
            console.log('=== 开始渲染图表 ===');
            console.log('输入数据:', data);

            if (!data) {
                console.log('从文本框获取数据...');
                try {
                    const rawData = elements.generatedData.value;
                    console.log('原始文本数据:', rawData);
                    data = JSON.parse(rawData);
                    console.log('解析后的数据:', data);
                } catch (e) {
                    console.error('JSON解析失败:', e);
                    showStatus('数据格式错误，请检查JSON格式', 'error');
                    return;
                }
            }

            // 验证和修复数据
            console.log('开始数据验证...');
            try {
                const originalData = JSON.parse(JSON.stringify(data)); // 深拷贝用于对比
                data = validateAndFixData(data);
                console.log('数据验证成功');
                console.log('原始数据:', originalData);
                console.log('修复后数据:', data);
            } catch (e) {
                console.error('数据验证失败:', e);
                showStatus(`数据验证失败: ${e.message}`, 'error');
                return;
            }

            // 保存当前图表数据
            currentChartData = data;

            // 更新图表标题
            elements.chartTitle.textContent = data.title || '图表预览';

            // 清除之前的图表
            if (chartInstance) {
                if (chartInstance.dispose) {
                    chartInstance.dispose();
                } else if (chartInstance.destroy) {
                    chartInstance.destroy();
                }
                chartInstance = null;
            }

            elements.chartContainer.innerHTML = '';
            elements.chartContainer.className = 'chart-container chart-ready';

            // 显示缩放控件
            elements.zoomControls.style.display = 'flex';
            currentZoom = 1;

            try {
                const framework = data.framework || elements.framework.value || 'auto';
                const actualFramework = framework === 'auto' ? getOptimalFramework(data.type) : framework;

                logRenderProcess(data, actualFramework);
                showStatus('正在渲染图表...', 'info', { duration: 0 });

                if (actualFramework === 'echarts') {
                    await renderECharts(data);
                } else if (actualFramework === 'd3') {
                    await renderD3(data);
                } else if (actualFramework === 'chartjs') {
                    await renderChartJS(data);
                } else if (actualFramework === 'custom') {
                    await renderCustomChart(data);
                } else {
                    await renderECharts(data); // 默认使用ECharts
                }

                debugLog('图表渲染完成');
                showStatus(`图表渲染成功`, 'success', {
                    description: `使用${actualFramework}框架渲染${getChartTypeName(data.type)}`
                });
            } catch (error) {
                const actualFramework = data.framework || elements.framework.value || 'auto';
                logRenderProcess(data, actualFramework, error);

                // 尝试使用备用框架
                if (data.type && ['bar', 'line', 'pie'].includes(data.type)) {
                    try {
                        showStatus('尝试使用备用渲染方案...', 'warning', { duration: 2000 });
                        await renderECharts(data);
                        debugLog('备用渲染成功');
                        showStatus('图表渲染成功（备用方案）', 'success');
                        return;
                    } catch (fallbackError) {
                        debugError('备用渲染失败:', fallbackError);
                    }
                }

                showStatus('图表渲染失败', 'error', {
                    description: error.message || '请检查数据格式或尝试其他图表类型',
                    showRetry: true,
                    retryCallback: () => renderChart(data)
                });
            }

            console.log('=== 渲染过程结束 ===');
        }

        // 获取最优框架
        function getOptimalFramework(type) {
            const d3Types = ['force', 'chord', 'voronoi', 'tree', 'org-chart', 'wordcloud'];
            const chartjsTypes = ['bar', 'line', 'pie', 'scatter', 'bubble', 'radar'];
            const customTypes = ['timeline', 'table']; // 需要自定义渲染的类型

            if (d3Types.includes(type)) return 'd3';
            if (chartjsTypes.includes(type)) return 'chartjs';
            if (customTypes.includes(type)) return 'custom';
            return 'echarts';
        }

        // 修复AI返回的不标准格式
        function fixAiResponseFormat(data) {
            debugLog('修复AI响应格式，原始数据:', data);

            // 修复字段名问题
            if (data.chartType && !data.type) {
                data.type = data.chartType;
                delete data.chartType;
            }

            // 修复Chart.js格式的数据结构
            if (data.data && data.data.datasets && !data.data.values && !data.data.data) {
                const dataset = data.data.datasets[0];
                if (dataset && dataset.data) {
                    // 对于基础图表，将datasets格式转换为我们的标准格式
                    if (['bar', 'line', 'pie'].includes(data.type)) {
                        data.data.values = dataset.data;
                        delete data.data.datasets;
                    }
                }
            }

            // 删除不需要的字段
            delete data.options;
            delete data.plugins;
            delete data.config;

            debugLog('修复后的数据:', data);
            return data;
        }

        // 从AI响应内容中提取JSON
        function extractJsonFromContent(content) {
            debugLog('开始提取JSON，原始内容:', content);

            // 方法1: 直接解析
            try {
                const jsonData = JSON.parse(content);
                debugLog('直接解析成功');
                return jsonData;
            } catch (e) {
                debugLog('直接解析失败:', e.message);
            }

            // 方法2: 从markdown代码块中提取
            const codeBlockPatterns = [
                /```json\s*(\{[\s\S]*?\})\s*```/i,
                /```\s*(\{[\s\S]*?\})\s*```/i,
                /`(\{[\s\S]*?\})`/i
            ];

            for (const pattern of codeBlockPatterns) {
                const match = content.match(pattern);
                if (match) {
                    try {
                        const jsonData = JSON.parse(match[1]);
                        debugLog('代码块解析成功，使用模式:', pattern);
                        return jsonData;
                    } catch (parseError) {
                        debugLog('代码块解析失败:', parseError.message);
                        continue;
                    }
                }
            }

            // 方法3: 查找JSON对象模式
            const jsonObjectPattern = /\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}/g;
            const matches = content.match(jsonObjectPattern);

            if (matches) {
                // 尝试解析最大的JSON对象
                const sortedMatches = matches.sort((a, b) => b.length - a.length);
                for (const match of sortedMatches) {
                    try {
                        const jsonData = JSON.parse(match);
                        debugLog('JSON对象模式解析成功');
                        return jsonData;
                    } catch (parseError) {
                        debugLog('JSON对象解析失败:', parseError.message);
                        continue;
                    }
                }
            }

            // 方法4: 尝试修复常见的JSON格式问题
            let fixedContent = content
                .replace(/```json\s*/i, '')
                .replace(/```\s*$/, '')
                .replace(/^[^{]*/, '')  // 移除开头的非JSON内容
                .replace(/[^}]*$/, '}') // 确保以}结尾
                .trim();

            try {
                const jsonData = JSON.parse(fixedContent);
                debugLog('修复后解析成功');
                return jsonData;
            } catch (e) {
                debugLog('修复后解析仍失败:', e.message);
            }

            debugError('所有JSON提取方法都失败了，原始内容:', content);
            throw new Error('无法从AI响应中提取有效的JSON数据');
        }

        // 生成针对特定图表类型的系统提示词
        function generateSystemPrompt(preferredType, userInput, forceType = false) {
            let basePrompt = `你是一个专业的数据可视化助手。用户会用自然语言描述他们的数据可视化需求，你需要：

1. 理解用户的需求${forceType && preferredType !== 'auto' ? '，使用指定的图表类型' : '，确定最适合的图表类型'}
2. 提取或生成合适的数据
3. 返回标准的JSON格式，包含图表配置

⚠️ 严格要求：
- 必须严格按照指定的JSON格式返回，不要添加、删除或修改任何字段名
- 字段名必须完全一致：使用"type"而不是"chartType"，使用"data"而不是"datasets"
- 如果用户提供了具体数值，请准确提取并使用
- 如果用户只是描述需求，请生成合理的示例数据
- 确保数据格式完全符合指定的图表类型要求
- 不要添加额外的配置字段如"options"、"plugins"等

只返回纯JSON，不要markdown代码块包装，不要其他解释。`;

            if (forceType && preferredType !== 'auto') {
                basePrompt += `\n\n🚨 强制要求：用户已在图表配置中选择了"${getChartTypeName(preferredType)}"，你必须使用此图表类型，不允许使用其他类型！`;
            }

            // 根据图表类型生成特定的提示词和数据格式要求
            const typeSpecificPrompts = {
                'bar': `
必须严格按照以下柱状图格式返回，不要添加任何其他字段：
{
  "type": "bar",
  "title": "图表标题",
  "data": {
    "labels": ["类别1", "类别2", "类别3"],
    "values": [数值1, 数值2, 数值3]
  }
}`,
                'line': `
必须严格按照以下折线图格式返回，不要添加任何其他字段：
{
  "type": "line",
  "title": "图表标题",
  "data": {
    "labels": ["时间点1", "时间点2", "时间点3"],
    "values": [数值1, 数值2, 数值3]
  }
}`,
                'pie': `
必须严格按照以下饼图格式返回，不要添加任何其他字段：
{
  "type": "pie",
  "title": "图表标题",
  "data": {
    "labels": ["部分1", "部分2", "部分3"],
    "values": [数值1, 数值2, 数值3]
  }
}`,
                'scatter': `
必须严格按照以下散点图格式返回，不要添加任何其他字段：
{
  "type": "scatter",
  "title": "图表标题",
  "data": {
    "data": [[x1, y1], [x2, y2], [x3, y3]]
  }
}`,
                'bubble': `
必须严格按照以下气泡图格式返回，不要添加任何其他字段：
{
  "type": "bubble",
  "title": "图表标题",
  "data": {
    "data": [[x1, y1, size1], [x2, y2, size2], [x3, y3, size3]]
  }
}`,
                'radar': `
必须严格按照以下雷达图格式返回，不要添加任何其他字段：
{
  "type": "radar",
  "title": "图表标题",
  "data": {
    "indicators": [
      {"name": "指标1", "max": 100},
      {"name": "指标2", "max": 100},
      {"name": "指标3", "max": 100}
    ],
    "series": [
      {"name": "数据系列1", "value": [80, 90, 70]}
    ]
  }
}`,
                'heatmap': `
必须严格按照以下热力图格式返回，不要添加任何其他字段：
{
  "type": "heatmap",
  "title": "图表标题",
  "data": {
    "xAxis": ["列1", "列2", "列3"],
    "yAxis": ["行1", "行2", "行3"],
    "data": [[0, 0, 值1], [0, 1, 值2], [1, 0, 值3]]
  }
}`,
                'boxplot': `
必须严格按照以下箱线图格式返回，不要添加任何其他字段：
{
  "type": "boxplot",
  "title": "图表标题",
  "data": {
    "categories": ["类别1", "类别2", "类别3"],
    "data": [[最小值, Q1, 中位数, Q3, 最大值], [min2, q1_2, med2, q3_2, max2]]
  }
}`,
                'sankey': `
必须严格按照以下桑基图格式返回，不要添加任何其他字段：
{
  "type": "sankey",
  "title": "图表标题",
  "data": {
    "nodes": [
      {"name": "节点1"},
      {"name": "节点2"},
      {"name": "节点3"}
    ],
    "links": [
      {"source": "节点1", "target": "节点2", "value": 数值1},
      {"source": "节点2", "target": "节点3", "value": 数值2}
    ]
  }
}`,
                'force': `
必须严格按照以下力导向图格式返回，不要添加任何其他字段：
{
  "type": "force",
  "title": "图表标题",
  "data": {
    "nodes": [
      {"id": "节点1", "name": "节点1"},
      {"id": "节点2", "name": "节点2"}
    ],
    "links": [
      {"source": "节点1", "target": "节点2", "value": 1}
    ]
  }
}`,
                'tree': `
必须严格按照以下树状图格式返回，不要添加任何其他字段：
{
  "type": "tree",
  "title": "图表标题",
  "data": {
    "name": "根节点",
    "children": [
      {
        "name": "子节点1",
        "children": [
          {"name": "叶节点1"},
          {"name": "叶节点2"}
        ]
      },
      {"name": "子节点2"}
    ]
  }
}`,
                'gantt': `
必须严格按照以下甘特图格式返回，不要添加任何其他字段：
{
  "type": "gantt",
  "title": "图表标题",
  "data": {
    "tasks": [
      {"name": "任务1", "start": "2024-01-01", "end": "2024-01-15", "status": "done"},
      {"name": "任务2", "start": "2024-01-10", "end": "2024-01-25", "status": "ongoing"}
    ]
  }
}`,
                'chord': `
必须严格按照以下弦图格式返回，不要添加任何其他字段：
{
  "type": "chord",
  "title": "图表标题",
  "data": {
    "labels": ["A", "B", "C"],
    "matrix": [
      [0, 5, 8],
      [5, 0, 3],
      [8, 3, 0]
    ]
  }
}`,
                'calendar-heatmap': `
必须严格按照以下日历热力图格式返回，不要添加任何其他字段：
{
  "type": "calendar-heatmap",
  "title": "图表标题",
  "data": {
    "data": [
      ["2024-01-01", 10],
      ["2024-01-02", 20],
      ["2024-01-03", 15]
    ]
  }
}`,
                'voronoi': `
必须严格按照以下Voronoi图格式返回，不要添加任何其他字段：
{
  "type": "voronoi",
  "title": "图表标题",
  "data": {
    "data": [[x1, y1], [x2, y2], [x3, y3]]
  }
}`,
                'choropleth': `
必须严格按照以下等值线图格式返回，不要添加任何其他字段：
{
  "type": "choropleth",
  "title": "图表标题",
  "data": {
    "geojsonUrl": "https://geo.datav.aliyun.com/areas_v3/bound/geojson?code=100000",
    "data": [
      {"name": "地区1", "value": 数值1},
      {"name": "地区2", "value": 数值2}
    ]
  }
}`,
                'map-scatter': `
必须严格按照以下地图散点图格式返回，不要添加任何其他字段：
{
  "type": "map-scatter",
  "title": "图表标题",
  "data": {
    "geojsonUrl": "https://geo.datav.aliyun.com/areas_v3/bound/geojson?code=100000",
    "points": [
      {"name": "点1", "coord": [经度, 纬度], "value": 数值1},
      {"name": "点2", "coord": [经度, 纬度], "value": 数值2}
    ]
  }
}`,
                'map-lines': `
必须严格按照以下地图连线图格式返回，不要添加任何其他字段：
{
  "type": "map-lines",
  "title": "图表标题",
  "data": {
    "geojsonUrl": "https://geo.datav.aliyun.com/areas_v3/bound/geojson?code=100000",
    "lines": [
      {"coords": [[起点经度, 起点纬度], [终点经度, 终点纬度]], "value": 数值1}
    ]
  }
}`,
                'org-chart': `
必须严格按照以下架构图格式返回，不要添加任何其他字段：
{
  "type": "org-chart",
  "title": "组织架构图",
  "data": {
    "name": "CEO/总经理",
    "children": [
      {
        "name": "部门1",
        "title": "部门职位",
        "children": [
          {"name": "员工1", "title": "职位1"},
          {"name": "员工2", "title": "职位2"}
        ]
      },
      {
        "name": "部门2",
        "title": "部门职位",
        "children": [
          {"name": "员工3", "title": "职位3"}
        ]
      }
    ]
  }
}`,
                'wordcloud': `
必须严格按照以下词频图格式返回，不要添加任何其他字段：
{
  "type": "wordcloud",
  "title": "词频图",
  "data": {
    "words": [
      {"text": "词语1", "value": 频次1},
      {"text": "词语2", "value": 频次2},
      {"text": "词语3", "value": 频次3}
    ]
  }
}`,
                'timeline': `
必须严格按照以下时间发展趋势图格式返回，不要添加任何其他字段：
{
  "type": "timeline",
  "title": "时间发展趋势图",
  "data": {
    "events": [
      {"time": "2020-01-01", "title": "事件1", "description": "事件描述1"},
      {"time": "2021-06-15", "title": "事件2", "description": "事件描述2"},
      {"time": "2022-12-31", "title": "事件3", "description": "事件描述3"}
    ]
  }
}`,
                'table': `
必须严格按照以下表格格式返回，不要添加任何其他字段：
{
  "type": "table",
  "title": "数据表格",
  "data": {
    "headers": ["列名1", "列名2", "列名3"],
    "rows": [
      ["数据1-1", "数据1-2", "数据1-3"],
      ["数据2-1", "数据2-2", "数据2-3"],
      ["数据3-1", "数据3-2", "数据3-3"]
    ]
  }
}`
            };

            let specificPrompt = '';
            if (preferredType !== 'auto' && typeSpecificPrompts[preferredType]) {
                const typeAction = forceType ? '必须使用' : '用户指定了';
                specificPrompt = `\n\n🎯 ${typeAction}图表类型：${preferredType}

⚠️ ${forceType ? '绝对要求' : '重要'}：必须严格按照以下格式生成数据，${forceType ? '不允许任何偏差' : '不允许任何偏差'}：
${typeSpecificPrompts[preferredType]}

📝 格式要求：
- type字段必须是"${preferredType}"，不能是其他值
- 字段名必须完全一致，不能使用chartType、datasets等其他名称
- 数据结构必须完全匹配，不能添加额外的配置字段
- 如果用户提供了具体数据，请准确提取并按格式填入
- 如果用户只描述需求，请生成合理的示例数据

${forceType ? '🚨 最终警告：违反格式要求将导致渲染失败！' : ''}
再次强调：只返回符合上述格式的纯JSON，不要任何额外内容！`;
            } else if (preferredType !== 'auto') {
                const typeAction = forceType ? '必须使用' : '偏好';
                specificPrompt = `\n\n用户${typeAction}的图表类型：${preferredType}，${forceType ? '必须' : '请优先考虑'}使用此类型，并确保返回标准的JSON格式。`;
            }

            return basePrompt + specificPrompt;
        }

        // 数据验证和修复函数
        function validateAndFixData(data) {
            if (!data || typeof data !== 'object') {
                throw new Error('无效的数据格式');
            }

            // 确保有type字段
            if (!data.type) {
                data.type = 'bar';
            }

            // 确保有data字段
            if (!data.data) {
                data.data = {};
            }

            // 根据图表类型验证和修复数据
            switch (data.type) {
                case 'bar':
                case 'line':
                case 'pie':
                    if (!data.data.labels && !data.data.values) {
                        data.data.labels = ['项目1', '项目2', '项目3'];
                        data.data.values = [30, 40, 50];
                    }
                    break;

                case 'scatter':
                case 'bubble':
                    if (!data.data.data || !Array.isArray(data.data.data)) {
                        data.data.data = [[10, 20], [30, 40], [50, 60]];
                    }
                    break;

                case 'radar':
                    if (!data.data.indicators) {
                        data.data.indicators = [
                            { name: '指标1', max: 100 },
                            { name: '指标2', max: 100 },
                            { name: '指标3', max: 100 }
                        ];
                    }
                    if (!data.data.series) {
                        data.data.series = [{ name: '数据1', value: [60, 70, 80] }];
                    }
                    break;
            }

            return data;
        }

        // 图表操作功能
        function downloadChart(format = 'png') {
            if (!chartInstance || !currentChartData) {
                showStatus('请先生成图表', 'error');
                return;
            }

            try {
                if (format === 'png') {
                    if (chartInstance.getDataURL) {
                        // ECharts
                        const url = chartInstance.getDataURL({
                            type: 'png',
                            pixelRatio: 2,
                            backgroundColor: '#fff'
                        });
                        downloadFile(url, `chart_${Date.now()}.png`);
                    } else {
                        // 其他框架，使用html2canvas
                        html2canvas(elements.chartContainer).then(canvas => {
                            const url = canvas.toDataURL('image/png');
                            downloadFile(url, `chart_${Date.now()}.png`);
                        });
                    }
                } else if (format === 'svg') {
                    if (chartInstance.getSvg) {
                        // ECharts SVG
                        const svgStr = chartInstance.getSvg();
                        const blob = new Blob([svgStr], { type: 'image/svg+xml' });
                        const url = URL.createObjectURL(blob);
                        downloadFile(url, `chart_${Date.now()}.svg`);
                    } else {
                        // D3 SVG
                        const svg = elements.chartContainer.querySelector('svg');
                        if (svg) {
                            const svgData = new XMLSerializer().serializeToString(svg);
                            const blob = new Blob([svgData], { type: 'image/svg+xml' });
                            const url = URL.createObjectURL(blob);
                            downloadFile(url, `chart_${Date.now()}.svg`);
                        } else {
                            showStatus('当前图表不支持SVG下载', 'error');
                        }
                    }
                }
                showStatus(`${format.toUpperCase()}文件下载成功`, 'success');
            } catch (error) {
                showStatus(`下载失败: ${error.message}`, 'error');
            }
        }

        function downloadFile(url, filename) {
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function copyChartImage() {
            if (!chartInstance) {
                showStatus('请先生成图表', 'error');
                return;
            }

            try {
                if (chartInstance.getDataURL) {
                    const url = chartInstance.getDataURL({
                        type: 'png',
                        pixelRatio: 2,
                        backgroundColor: '#fff'
                    });

                    // 将base64转换为blob
                    fetch(url)
                        .then(res => res.blob())
                        .then(blob => {
                            navigator.clipboard.write([
                                new ClipboardItem({ 'image/png': blob })
                            ]);
                            showStatus('图表已复制到剪贴板', 'success');
                        })
                        .catch(() => {
                            showStatus('复制失败，请尝试下载', 'error');
                        });
                } else {
                    showStatus('当前图表不支持复制功能', 'error');
                }
            } catch (error) {
                showStatus(`复制失败: ${error.message}`, 'error');
            }
        }

        function toggleFullscreen() {
            const chartArea = elements.chartContainer.closest('.chart-area');

            if (!document.fullscreenElement) {
                chartArea.requestFullscreen().then(() => {
                    elements.fullscreenBtn.innerHTML = '🔍';
                    elements.fullscreenBtn.title = '退出全屏';
                    if (chartInstance && chartInstance.resize) {
                        setTimeout(() => chartInstance.resize(), 100);
                    }
                });
            } else {
                document.exitFullscreen().then(() => {
                    elements.fullscreenBtn.innerHTML = '🔍';
                    elements.fullscreenBtn.title = '全屏显示';
                    if (chartInstance && chartInstance.resize) {
                        setTimeout(() => chartInstance.resize(), 100);
                    }
                });
            }
        }

        function zoomChart(factor) {
            currentZoom *= factor;
            currentZoom = Math.max(0.5, Math.min(3, currentZoom)); // 限制缩放范围

            elements.chartContainer.style.transform = `scale(${currentZoom})`;
            elements.chartContainer.style.transformOrigin = 'center center';

            if (chartInstance && chartInstance.resize) {
                setTimeout(() => chartInstance.resize(), 100);
            }
        }

        function resetZoom() {
            currentZoom = 1;
            elements.chartContainer.style.transform = 'scale(1)';
            if (chartInstance && chartInstance.resize) {
                setTimeout(() => chartInstance.resize(), 100);
            }
        }

        // 初始化检查和用户引导
        function initializeApp() {
            loadConfig();
            showChartTypeHint(elements.chartType.value);

            // 检查配置完整性
            setTimeout(() => {
                if (!validateApiConfig()) {
                    showStatus('欢迎使用智能图表生成器！', 'info', {
                        description: '请先在左侧配置您的API信息，然后就可以开始创建图表了。',
                        duration: 8000
                    });
                } else {
                    showStatus('系统已就绪', 'success', {
                        description: '您可以开始描述您的图表需求了！',
                        duration: 3000
                    });
                }
            }, 1000);
        }

        // 页面可见性变化处理
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                // 页面重新可见时，清除可能的错误状态
                if (elements.generateBtn.disabled && !elements.loading.style.display) {
                    elements.generateBtn.disabled = false;
                    elements.generateBtn.innerHTML = '✨ AI生成图表';
                }
            }
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + Enter 快速生成
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault();
                if (!elements.generateBtn.disabled && elements.userInput.value.trim()) {
                    generateChart();
                }
            }

            // Escape 清除状态消息
            if (e.key === 'Escape') {
                elements.statusMessage.innerHTML = '';
            }
        });

        // 初始化
        initializeApp();

        // 事件监听器
        elements.generateBtn.addEventListener('click', generateChart);
        elements.renderBtn.addEventListener('click', () => renderChart());

        // 示例菜单功能
        elements.exampleBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            const menu = elements.exampleMenu;
            menu.style.display = menu.style.display === 'none' ? 'block' : 'none';
        });

        // 点击其他地方关闭菜单
        document.addEventListener('click', () => {
            elements.exampleMenu.style.display = 'none';
        });

        // 示例选项处理
        document.querySelectorAll('.example-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.stopPropagation();
                const exampleType = item.dataset.example;

                const examples = {
                    'bar': '显示2023年各季度销售数据的柱状图，Q1: 120万，Q2: 150万，Q3: 180万，Q4: 200万',
                    'pie': '显示市场份额分布的饼图：苹果40%，三星25%，华为20%，小米10%，其他5%',
                    'line': '显示2023年每月用户增长趋势：1月1000人，2月1200人，3月1500人，4月1800人，5月2200人',
                    'org-chart': '显示公司组织架构：CEO张总，下设技术部（部长李工，员工小王、小李），市场部（部长王经理，员工小张、小赵）',
                    'wordcloud': '显示文本词频分析：人工智能出现50次，机器学习30次，深度学习25次，数据科学20次，算法15次',
                    'timeline': '显示公司发展历程：2020年1月成立，2021年6月获得A轮融资，2022年12月产品上线，2023年8月用户破百万',
                    'table': '显示员工信息表格：姓名、部门、职位、薪资等信息',
                    'test': null // 直接测试
                };

                if (exampleType === 'test') {
                    // 测试强制图表类型功能
                    elements.userInput.value = '显示销售数据：Q1: 100万，Q2: 120万，Q3: 150万，Q4: 180万';
                    elements.chartType.value = 'pie'; // 强制设置为饼图
                    showStatus('测试强制图表类型功能', 'info', {
                        description: '将销售数据强制生成为饼图，测试AI是否严格遵循图表类型要求'
                    });

                    // 延迟执行以显示提示
                    setTimeout(() => {
                        generateChart();
                    }, 1000);
                } else {
                    elements.userInput.value = examples[exampleType];
                    showStatus(`已填入${item.textContent}`, 'success', { duration: 2000 });
                }

                elements.exampleMenu.style.display = 'none';
            });
        });

        elements.copyBtn.addEventListener('click', () => {
            elements.generatedData.select();
            document.execCommand('copy');
            showStatus('数据已复制到剪贴板', 'success');
        });

        // 新增的图表操作事件监听器
        elements.downloadPngBtn.addEventListener('click', () => downloadChart('png'));
        elements.downloadSvgBtn.addEventListener('click', () => downloadChart('svg'));
        elements.copyChartBtn.addEventListener('click', copyChartImage);
        elements.fullscreenBtn.addEventListener('click', toggleFullscreen);
        elements.refreshBtn.addEventListener('click', () => {
            if (currentChartData) {
                renderChart(currentChartData);
                showStatus('图表已刷新', 'success');
            }
        });

        // 缩放控件事件监听器
        elements.zoomInBtn.addEventListener('click', () => zoomChart(1.2));
        elements.zoomOutBtn.addEventListener('click', () => zoomChart(0.8));
        elements.zoomResetBtn.addEventListener('click', resetZoom);

        // 调试模式切换
        elements.debugMode.addEventListener('change', () => {
            debugMode = elements.debugMode.checked;
            debugLog('调试模式已', debugMode ? '开启' : '关闭');
            if (debugMode) {
                console.log('🐛 调试模式已开启，将显示详细的API调用和渲染过程信息');
            }
        });

        // 系统设置对话框事件
        elements.settingsBtn.addEventListener('click', () => {
            elements.settingsModal.style.display = 'flex';
        });

        elements.closeSettingsBtn.addEventListener('click', () => {
            elements.settingsModal.style.display = 'none';
        });

        // 点击对话框外部关闭
        elements.settingsModal.addEventListener('click', (e) => {
            if (e.target === elements.settingsModal) {
                elements.settingsModal.style.display = 'none';
            }
        });

        // 保存设置
        elements.saveSettingsBtn.addEventListener('click', () => {
            saveConfig();
            elements.settingsModal.style.display = 'none';
            showStatus('设置已保存', 'success', {
                description: '您的配置已安全保存到本地浏览器'
            });
        });

        // 重置设置
        elements.resetSettingsBtn.addEventListener('click', () => {
            if (confirm('确定要重置所有设置到默认值吗？')) {
                // 重置为默认值
                elements.apiUrl.value = 'https://openrouter.ai/api/v1/chat/completions';
                elements.apiKey.value = '';
                elements.model.value = 'deepseek/deepseek-chat-v3-0324:free';
                elements.framework.value = 'auto';
                elements.chartType.value = 'auto';
                elements.debugMode.checked = true;
                elements.autoRender.checked = true;

                // 更新全局变量
                debugMode = true;

                showStatus('设置已重置', 'success', {
                    description: '所有设置已恢复为默认值'
                });
            }
        });

        // ESC键关闭对话框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && elements.settingsModal.style.display === 'flex') {
                elements.settingsModal.style.display = 'none';
            }
        });

        // 图表类型选择变化时自动重新生成
        elements.chartType.addEventListener('change', async () => {
            const userInput = elements.userInput.value.trim();
            const selectedType = elements.chartType.value;

            // 显示图表类型提示
            showChartTypeHint(selectedType);

            if (userInput && selectedType !== 'auto') {
                // 显示提示信息
                showStatus(`正在强制生成${getChartTypeName(selectedType)}...`, 'info', {
                    description: 'AI将严格按照选定的图表类型生成数据'
                });

                // 延迟一点时间让用户看到提示
                setTimeout(() => {
                    generateChart(); // 这里会使用强制模式
                }, 500);
            } else if (userInput && selectedType === 'auto') {
                // 如果选择了自动，也重新生成以应用新的逻辑
                showStatus('正在重新分析最适合的图表类型...', 'info');
                setTimeout(() => {
                    generateChart();
                }, 500);
            }
        });

        // 获取图表类型的中文名称
        function getChartTypeName(type) {
            const typeNames = {
                'bar': '柱状图',
                'line': '折线图',
                'pie': '饼图',
                'scatter': '散点图',
                'bubble': '气泡图',
                'radar': '雷达图',
                'boxplot': '箱线图',
                'heatmap': '热力图',
                'calendar-heatmap': '日历热力图',
                'gantt': '甘特图',
                'force': '力导向图',
                'sankey': '桑基图',
                'tree': '树状图',
                'chord': '弦图',
                'voronoi': 'Voronoi图',
                'choropleth': '等值线图',
                'map-scatter': '地图散点图',
                'map-lines': '地图连线图',
                'org-chart': '架构图',
                'wordcloud': '词频图',
                'timeline': '时间发展趋势图',
                'table': '表格'
            };
            return typeNames[type] || type;
        }

        // 显示图表类型提示
        function showChartTypeHint(type) {
            const hints = {
                'auto': '让AI自动分析你的数据并选择最合适的图表类型。',
                'bar': '适合比较不同类别的数值大小。<br><strong>示例：</strong>"显示各部门销售额：销售部100万，市场部80万，技术部60万"',
                'line': '适合显示数据随时间的变化趋势。<br><strong>示例：</strong>"显示2023年每月用户增长：1月1000人，2月1200人，3月1500人"',
                'pie': '适合显示各部分占整体的比例关系。<br><strong>示例：</strong>"显示市场份额分布：苹果40%，三星25%，华为20%，其他15%"',
                'scatter': '适合显示两个变量之间的相关关系。<br><strong>示例：</strong>"显示身高体重关系：(170,65), (175,70), (180,75)"',
                'bubble': '适合显示三维数据关系，气泡大小表示第三个维度。<br><strong>示例：</strong>"显示城市人口GDP关系：北京(100,3000,2000万), 上海(90,2800,2500万)"',
                'radar': '适合多维度数据对比分析。<br><strong>示例：</strong>"员工能力评估：技术能力90分，沟通能力80分，管理能力70分"',
                'heatmap': '适合显示矩阵数据的热度分布。<br><strong>示例：</strong>"显示网站访问热力图：周一上午访问量1000，周一下午800"',
                'sankey': '适合显示流程中的数据流向和转换。<br><strong>示例：</strong>"显示能源流向：煤炭→发电厂→居民用电"',
                'force': '适合显示网络关系和节点连接。<br><strong>示例：</strong>"显示社交网络关系：张三认识李四，李四认识王五"',
                'tree': '适合显示层级结构和分类关系。<br><strong>示例：</strong>"显示公司组织架构：总经理→部门经理→员工"',
                'gantt': '适合显示项目进度和时间安排。<br><strong>示例：</strong>"项目计划：需求分析1月1日-1月15日，开发1月16日-2月28日"',
                'org-chart': '适合显示组织架构和层级关系。<br><strong>示例：</strong>"显示公司架构：CEO张总，技术部李部长（员工小王、小李），市场部王经理（员工小张、小赵）"',
                'wordcloud': '适合显示文本词频和关键词分析。<br><strong>示例：</strong>"分析文本词频：人工智能出现50次，机器学习30次，深度学习25次，数据科学20次"',
                'timeline': '适合显示时间发展历程和重要事件。<br><strong>示例：</strong>"公司发展历程：2020年1月成立，2021年6月A轮融资，2022年12月产品上线，2023年8月用户破百万"',
                'table': '适合显示结构化数据和详细信息。<br><strong>示例：</strong>"员工信息表：包含姓名、部门、职位、薪资等字段的数据表格"'
            };

            if (type === 'auto') {
                elements.chartTypeHint.style.display = 'none';
            } else {
                elements.chartTypeHint.style.display = 'block';
                elements.chartTypeHintText.innerHTML = hints[type] || `${getChartTypeName(type)}的数据格式提示暂未配置。`;
            }
        }

        // 自定义图表渲染（时间线和表格）
        async function renderCustomChart(data) {
            const container = elements.chartContainer;
            container.innerHTML = ''; // 清空容器

            if (data.type === 'timeline') {
                await renderTimeline(data, container);
            } else if (data.type === 'table') {
                await renderTable(data, container);
            } else {
                throw new Error(`不支持的自定义图表类型: ${data.type}`);
            }
        }

        // 时间发展趋势图渲染
        async function renderTimeline(data, container) {
            const events = data.data.events || [];

            // 创建时间线容器
            const timelineDiv = document.createElement('div');
            timelineDiv.style.cssText = `
                padding: 20px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 12px;
                color: white;
                position: relative;
                overflow-y: auto;
                max-height: 500px;
            `;

            // 添加标题
            const title = document.createElement('h3');
            title.textContent = data.title || '时间发展趋势图';
            title.style.cssText = `
                margin: 0 0 30px 0;
                text-align: center;
                font-size: 1.5rem;
                font-weight: 600;
            `;
            timelineDiv.appendChild(title);

            // 创建时间线
            const timelineContainer = document.createElement('div');
            timelineContainer.style.cssText = `
                position: relative;
                padding-left: 30px;
            `;

            // 添加时间线主线
            const mainLine = document.createElement('div');
            mainLine.style.cssText = `
                position: absolute;
                left: 15px;
                top: 0;
                bottom: 0;
                width: 2px;
                background: rgba(255, 255, 255, 0.3);
            `;
            timelineContainer.appendChild(mainLine);

            // 添加事件
            events.forEach((event, index) => {
                const eventDiv = document.createElement('div');
                eventDiv.style.cssText = `
                    position: relative;
                    margin-bottom: 30px;
                    padding-left: 40px;
                `;

                // 事件节点
                const node = document.createElement('div');
                node.style.cssText = `
                    position: absolute;
                    left: -8px;
                    top: 5px;
                    width: 16px;
                    height: 16px;
                    background: white;
                    border-radius: 50%;
                    border: 3px solid #667eea;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                `;
                eventDiv.appendChild(node);

                // 事件内容
                const content = document.createElement('div');
                content.style.cssText = `
                    background: rgba(255, 255, 255, 0.1);
                    padding: 15px;
                    border-radius: 8px;
                    backdrop-filter: blur(10px);
                `;

                const eventTime = document.createElement('div');
                eventTime.textContent = event.time;
                eventTime.style.cssText = `
                    font-size: 0.9rem;
                    opacity: 0.8;
                    margin-bottom: 5px;
                `;

                const eventTitle = document.createElement('div');
                eventTitle.textContent = event.title;
                eventTitle.style.cssText = `
                    font-size: 1.1rem;
                    font-weight: 600;
                    margin-bottom: 8px;
                `;

                const eventDesc = document.createElement('div');
                eventDesc.textContent = event.description;
                eventDesc.style.cssText = `
                    font-size: 0.95rem;
                    line-height: 1.4;
                    opacity: 0.9;
                `;

                content.appendChild(eventTime);
                content.appendChild(eventTitle);
                content.appendChild(eventDesc);
                eventDiv.appendChild(content);

                timelineContainer.appendChild(eventDiv);
            });

            timelineDiv.appendChild(timelineContainer);
            container.appendChild(timelineDiv);
        }

        // 表格渲染
        async function renderTable(data, container) {
            const headers = data.data.headers || [];
            const rows = data.data.rows || [];

            // 创建表格容器
            const tableContainer = document.createElement('div');
            tableContainer.style.cssText = `
                padding: 20px;
                background: white;
                border-radius: 12px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                overflow: auto;
                max-height: 500px;
            `;

            // 添加标题
            const title = document.createElement('h3');
            title.textContent = data.title || '数据表格';
            title.style.cssText = `
                margin: 0 0 20px 0;
                color: #1f2328;
                font-size: 1.3rem;
                font-weight: 600;
                text-align: center;
            `;
            tableContainer.appendChild(title);

            // 创建表格
            const table = document.createElement('table');
            table.style.cssText = `
                width: 100%;
                border-collapse: collapse;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-size: 0.9rem;
            `;

            // 创建表头
            if (headers.length > 0) {
                const thead = document.createElement('thead');
                const headerRow = document.createElement('tr');

                headers.forEach(header => {
                    const th = document.createElement('th');
                    th.textContent = header;
                    th.style.cssText = `
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        padding: 12px 16px;
                        text-align: left;
                        font-weight: 600;
                        border: 1px solid #e2e8f0;
                    `;
                    headerRow.appendChild(th);
                });

                thead.appendChild(headerRow);
                table.appendChild(thead);
            }

            // 创建表体
            if (rows.length > 0) {
                const tbody = document.createElement('tbody');

                rows.forEach((row, rowIndex) => {
                    const tr = document.createElement('tr');
                    tr.style.cssText = `
                        background: ${rowIndex % 2 === 0 ? '#f8fafc' : 'white'};
                        transition: background-color 0.2s;
                    `;

                    // 添加悬浮效果
                    tr.addEventListener('mouseenter', () => {
                        tr.style.background = '#e2e8f0';
                    });
                    tr.addEventListener('mouseleave', () => {
                        tr.style.background = rowIndex % 2 === 0 ? '#f8fafc' : 'white';
                    });

                    row.forEach(cell => {
                        const td = document.createElement('td');
                        td.textContent = cell;
                        td.style.cssText = `
                            padding: 12px 16px;
                            border: 1px solid #e2e8f0;
                            color: #1f2328;
                        `;
                        tr.appendChild(td);
                    });

                    tbody.appendChild(tr);
                });

                table.appendChild(tbody);
            }

            tableContainer.appendChild(table);
            container.appendChild(tableContainer);
        }

        // ECharts渲染
        async function renderECharts(data) {
            chartInstance = echarts.init(elements.chartContainer);

            let option = {};

            // 数据预处理和验证
            const chartData = data.data || {};
            const labels = chartData.labels || chartData.categories || [];
            const values = chartData.values || chartData.data || [];

            switch (data.type) {
                case 'bar':
                case 'line':
                    // 确保数据格式正确
                    if (labels.length === 0 && values.length > 0) {
                        // 如果没有标签，生成默认标签
                        for (let i = 0; i < values.length; i++) {
                            labels.push(`项目${i + 1}`);
                        }
                    }

                    option = {
                        title: { text: data.title || '', left: 'center' },
                        tooltip: { trigger: 'axis' },
                        xAxis: {
                            type: 'category',
                            data: labels
                        },
                        yAxis: { type: 'value' },
                        series: [{
                            type: data.type,
                            data: values,
                            itemStyle: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                    { offset: 0, color: '#667eea' },
                                    { offset: 1, color: '#764ba2' }
                                ])
                            }
                        }]
                    };
                    break;

                case 'pie':
                    let pieData = [];
                    if (labels.length > 0 && values.length > 0) {
                        pieData = labels.map((label, i) => ({
                            name: label,
                            value: values[i] || 0
                        }));
                    } else if (chartData.data && Array.isArray(chartData.data)) {
                        pieData = chartData.data;
                    } else {
                        // 默认数据
                        pieData = [
                            { name: '项目1', value: 30 },
                            { name: '项目2', value: 40 },
                            { name: '项目3', value: 30 }
                        ];
                    }

                    option = {
                        title: { text: data.title || '', left: 'center' },
                        tooltip: { trigger: 'item' },
                        legend: { orient: 'vertical', left: 'left' },
                        series: [{
                            type: 'pie',
                            radius: ['40%', '70%'],
                            data: pieData,
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            }
                        }]
                    };
                    break;

                case 'scatter':
                case 'bubble':
                    const scatterData = chartData.data || [];
                    // 确保散点数据格式正确 [[x,y], [x,y,size], ...]
                    const processedScatterData = scatterData.map(point => {
                        if (Array.isArray(point)) {
                            return point;
                        } else if (typeof point === 'object') {
                            return [point.x || 0, point.y || 0, point.size || point.value || 10];
                        }
                        return [0, 0, 10];
                    });

                    option = {
                        title: { text: data.title || '', left: 'center' },
                        tooltip: {
                            trigger: 'item',
                            formatter: function(params) {
                                const point = params.data;
                                return `X: ${point[0]}<br/>Y: ${point[1]}${point[2] ? '<br/>大小: ' + point[2] : ''}`;
                            }
                        },
                        xAxis: { type: 'value' },
                        yAxis: { type: 'value' },
                        series: [{
                            type: 'scatter',
                            data: processedScatterData,
                            symbolSize: data.type === 'bubble' ?
                                (val) => Math.sqrt(val[2] || 20) * 2 : 8
                        }]
                    };
                    break;

                case 'radar':
                    const indicators = chartData.indicators || [
                        { name: '指标1', max: 100 },
                        { name: '指标2', max: 100 },
                        { name: '指标3', max: 100 }
                    ];
                    const radarSeries = chartData.series || [
                        { name: '数据1', value: [60, 70, 80] }
                    ];

                    option = {
                        title: { text: data.title || '', left: 'center' },
                        tooltip: {},
                        radar: {
                            indicator: indicators
                        },
                        series: [{
                            type: 'radar',
                            data: radarSeries
                        }]
                    };
                    break;

                case 'sankey':
                    const sankeyNodes = chartData.nodes || [];
                    const sankeyLinks = chartData.links || [];

                    option = {
                        title: { text: data.title || '', left: 'center' },
                        tooltip: { trigger: 'item' },
                        series: [{
                            type: 'sankey',
                            data: data.data.nodes || [],
                            links: data.data.links || [],
                            emphasis: { focus: 'adjacency' },
                            lineStyle: { curveness: 0.5 }
                        }]
                    };
                    break;

                case 'heatmap':
                    const heatmapData = chartData.data || [];
                    const xAxisData = chartData.xAxis || chartData.x || [];
                    const yAxisData = chartData.yAxis || chartData.y || [];

                    // 如果没有轴数据，从热力图数据中提取
                    if (xAxisData.length === 0 && heatmapData.length > 0) {
                        const xValues = [...new Set(heatmapData.map(item => item[0]))].sort();
                        xAxisData.push(...xValues);
                    }
                    if (yAxisData.length === 0 && heatmapData.length > 0) {
                        const yValues = [...new Set(heatmapData.map(item => item[1]))].sort();
                        yAxisData.push(...yValues);
                    }

                    const maxValue = heatmapData.length > 0 ?
                        Math.max(...heatmapData.map(item => item[2] || 0)) : 100;

                    option = {
                        title: { text: data.title || '', left: 'center' },
                        tooltip: {
                            position: 'top',
                            formatter: function(params) {
                                return `X: ${params.data[0]}<br/>Y: ${params.data[1]}<br/>值: ${params.data[2]}`;
                            }
                        },
                        grid: { height: '50%', top: '15%' },
                        xAxis: {
                            type: 'category',
                            data: xAxisData,
                            splitArea: { show: true }
                        },
                        yAxis: {
                            type: 'category',
                            data: yAxisData,
                            splitArea: { show: true }
                        },
                        visualMap: {
                            min: 0,
                            max: maxValue,
                            calculable: true,
                            orient: 'horizontal',
                            left: 'center',
                            bottom: '5%'
                        },
                        series: [{
                            type: 'heatmap',
                            data: heatmapData,
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            }
                        }]
                    };
                    break;

                default:
                    // 默认处理 - 创建一个简单的柱状图
                    const defaultLabels = labels.length > 0 ? labels : ['项目1', '项目2', '项目3', '项目4', '项目5'];
                    const defaultValues = values.length > 0 ? values : [10, 20, 30, 40, 50];

                    option = {
                        title: { text: data.title || `${data.type}图表`, left: 'center' },
                        tooltip: { trigger: 'axis' },
                        xAxis: {
                            type: 'category',
                            data: defaultLabels
                        },
                        yAxis: { type: 'value' },
                        series: [{
                            type: 'bar',
                            data: defaultValues,
                            itemStyle: {
                                color: '#667eea'
                            }
                        }]
                    };
            }

            chartInstance.setOption(option);

            // 响应式
            window.addEventListener('resize', () => {
                if (chartInstance) {
                    chartInstance.resize();
                }
            });
        }

        // Chart.js渲染
        async function renderChartJS(data) {
            const canvas = document.createElement('canvas');
            elements.chartContainer.appendChild(canvas);

            const ctx = canvas.getContext('2d');

            // 数据预处理
            const chartData = data.data || {};
            const labels = chartData.labels || chartData.categories || [];
            const values = chartData.values || chartData.data || [];

            // 确保有数据
            if (labels.length === 0 && values.length > 0) {
                for (let i = 0; i < values.length; i++) {
                    labels.push(`项目${i + 1}`);
                }
            }

            let config = {};

            switch (data.type) {
                case 'bar':
                case 'line':
                    config = {
                        type: data.type,
                        data: {
                            labels: labels,
                            datasets: [{
                                label: data.title || '数据系列',
                                data: values,
                                backgroundColor: data.type === 'bar' ?
                                    'rgba(102, 126, 234, 0.8)' : 'rgba(102, 126, 234, 0.2)',
                                borderColor: 'rgba(102, 126, 234, 1)',
                                borderWidth: 2,
                                fill: data.type === 'line'
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                title: {
                                    display: !!data.title,
                                    text: data.title || ''
                                }
                            }
                        }
                    };
                    break;

                case 'pie':
                    // 处理饼图数据
                    let pieLabels = labels;
                    let pieValues = values;

                    if (pieLabels.length === 0 && pieValues.length === 0) {
                        // 默认数据
                        pieLabels = ['项目1', '项目2', '项目3'];
                        pieValues = [30, 40, 30];
                    }

                    config = {
                        type: 'pie',
                        data: {
                            labels: pieLabels,
                            datasets: [{
                                data: pieValues,
                                backgroundColor: [
                                    '#667eea', '#764ba2', '#f093fb', '#f5576c',
                                    '#4facfe', '#00f2fe', '#43e97b', '#38f9d7'
                                ]
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                title: {
                                    display: !!data.title,
                                    text: data.title || ''
                                }
                            }
                        }
                    };
                    break;

                case 'scatter':
                    const scatterPoints = chartData.data || [];
                    const scatterData = scatterPoints.map(point => {
                        if (Array.isArray(point)) {
                            return { x: point[0], y: point[1] };
                        } else if (typeof point === 'object') {
                            return { x: point.x || 0, y: point.y || 0 };
                        }
                        return { x: 0, y: 0 };
                    });

                    config = {
                        type: 'scatter',
                        data: {
                            datasets: [{
                                label: data.title || '散点数据',
                                data: scatterData,
                                backgroundColor: 'rgba(102, 126, 234, 0.8)',
                                borderColor: 'rgba(102, 126, 234, 1)',
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                title: {
                                    display: !!data.title,
                                    text: data.title || ''
                                }
                            }
                        }
                    };
                    break;

                default:
                    throw new Error(`Chart.js不支持${data.type}类型的图表，请选择ECharts或D3.js`);
            }

            chartInstance = new Chart(ctx, config);
        }

        // D3渲染 (简化版本)
        async function renderD3(data) {
            const width = elements.chartContainer.clientWidth;
            const height = elements.chartContainer.clientHeight;

            const svg = d3.select(elements.chartContainer)
                .append('svg')
                .attr('width', width)
                .attr('height', height);

            // 添加标题
            if (data.title) {
                svg.append('text')
                    .attr('x', width / 2)
                    .attr('y', 30)
                    .attr('text-anchor', 'middle')
                    .style('font-size', '18px')
                    .style('font-weight', 'bold')
                    .text(data.title);
            }

            switch (data.type) {
                case 'force':
                    renderForceGraph(svg, data, width, height);
                    break;

                case 'chord':
                    renderChordDiagram(svg, data, width, height);
                    break;

                case 'org-chart':
                    renderOrgChart(svg, data, width, height);
                    break;

                case 'wordcloud':
                    renderWordCloud(svg, data, width, height);
                    break;

                default:
                    // 简单的条形图作为默认
                    renderSimpleBarChart(svg, data, width, height);
            }
        }

        // D3力导向图
        function renderForceGraph(svg, data, width, height) {
            const nodes = data.data.nodes || [];
            const links = data.data.links || [];

            const simulation = d3.forceSimulation(nodes)
                .force('link', d3.forceLink(links).id(d => d.id))
                .force('charge', d3.forceManyBody().strength(-300))
                .force('center', d3.forceCenter(width / 2, height / 2));

            const link = svg.append('g')
                .selectAll('line')
                .data(links)
                .enter().append('line')
                .attr('stroke', '#999')
                .attr('stroke-opacity', 0.6)
                .attr('stroke-width', 2);

            const node = svg.append('g')
                .selectAll('circle')
                .data(nodes)
                .enter().append('circle')
                .attr('r', 8)
                .attr('fill', '#667eea')
                .call(d3.drag()
                    .on('start', dragstarted)
                    .on('drag', dragged)
                    .on('end', dragended));

            node.append('title')
                .text(d => d.name || d.id);

            simulation.on('tick', () => {
                link
                    .attr('x1', d => d.source.x)
                    .attr('y1', d => d.source.y)
                    .attr('x2', d => d.target.x)
                    .attr('y2', d => d.target.y);

                node
                    .attr('cx', d => d.x)
                    .attr('cy', d => d.y);
            });

            function dragstarted(event, d) {
                if (!event.active) simulation.alphaTarget(0.3).restart();
                d.fx = d.x;
                d.fy = d.y;
            }

            function dragged(event, d) {
                d.fx = event.x;
                d.fy = event.y;
            }

            function dragended(event, d) {
                if (!event.active) simulation.alphaTarget(0);
                d.fx = null;
                d.fy = null;
            }
        }

        // D3简单条形图
        function renderSimpleBarChart(svg, data, width, height) {
            const margin = { top: 50, right: 30, bottom: 40, left: 40 };
            const chartWidth = width - margin.left - margin.right;
            const chartHeight = height - margin.top - margin.bottom;

            const values = data.data.values || [10, 20, 30, 40, 50];
            const labels = data.data.labels || values.map((_, i) => `Item ${i + 1}`);

            const xScale = d3.scaleBand()
                .domain(labels)
                .range([0, chartWidth])
                .padding(0.1);

            const yScale = d3.scaleLinear()
                .domain([0, d3.max(values)])
                .range([chartHeight, 0]);

            const g = svg.append('g')
                .attr('transform', `translate(${margin.left},${margin.top})`);

            g.selectAll('.bar')
                .data(values)
                .enter().append('rect')
                .attr('class', 'bar')
                .attr('x', (d, i) => xScale(labels[i]))
                .attr('width', xScale.bandwidth())
                .attr('y', d => yScale(d))
                .attr('height', d => chartHeight - yScale(d))
                .attr('fill', '#667eea');

            g.append('g')
                .attr('transform', `translate(0,${chartHeight})`)
                .call(d3.axisBottom(xScale));

            g.append('g')
                .call(d3.axisLeft(yScale));
        }

        // D3弦图 (简化版)
        function renderChordDiagram(svg, data, width, height) {
            // 这里可以添加弦图的实现
            // 为了简化，我们显示一个提示
            svg.append('text')
                .attr('x', width / 2)
                .attr('y', height / 2)
                .attr('text-anchor', 'middle')
                .style('font-size', '16px')
                .text('弦图功能开发中...');
        }

        // D3架构图渲染
        function renderOrgChart(svg, data, width, height) {
            const root = d3.hierarchy(data.data);
            const treeLayout = d3.tree().size([width - 100, height - 100]);
            treeLayout(root);

            // 创建主容器
            const g = svg.append('g')
                .attr('transform', 'translate(50, 50)');

            // 绘制连接线
            g.selectAll('.link')
                .data(root.links())
                .enter().append('path')
                .attr('class', 'link')
                .attr('d', d3.linkVertical()
                    .x(d => d.x)
                    .y(d => d.y))
                .style('fill', 'none')
                .style('stroke', '#ccc')
                .style('stroke-width', 2);

            // 绘制节点
            const node = g.selectAll('.node')
                .data(root.descendants())
                .enter().append('g')
                .attr('class', 'node')
                .attr('transform', d => `translate(${d.x},${d.y})`);

            // 添加节点背景
            node.append('rect')
                .attr('width', 120)
                .attr('height', 60)
                .attr('x', -60)
                .attr('y', -30)
                .style('fill', d => d.depth === 0 ? '#667eea' : '#4299e1')
                .style('stroke', '#2d3748')
                .style('stroke-width', 1)
                .style('rx', 8);

            // 添加姓名文本
            node.append('text')
                .attr('dy', -5)
                .attr('text-anchor', 'middle')
                .style('fill', 'white')
                .style('font-size', '12px')
                .style('font-weight', 'bold')
                .text(d => d.data.name);

            // 添加职位文本
            node.append('text')
                .attr('dy', 15)
                .attr('text-anchor', 'middle')
                .style('fill', 'white')
                .style('font-size', '10px')
                .text(d => d.data.title || '');
        }

        // D3词频图渲染
        function renderWordCloud(svg, data, width, height) {
            const words = data.data.words || [];

            // 计算字体大小比例
            const maxValue = Math.max(...words.map(w => w.value));
            const minValue = Math.min(...words.map(w => w.value));
            const fontScale = d3.scaleLinear()
                .domain([minValue, maxValue])
                .range([12, 48]);

            // 颜色比例
            const colorScale = d3.scaleOrdinal(d3.schemeCategory10);

            // 简单的词云布局（网格布局）
            const cols = Math.ceil(Math.sqrt(words.length));
            const cellWidth = width / cols;
            const cellHeight = height / Math.ceil(words.length / cols);

            const g = svg.append('g');

            words.forEach((word, i) => {
                const row = Math.floor(i / cols);
                const col = i % cols;
                const x = col * cellWidth + cellWidth / 2;
                const y = row * cellHeight + cellHeight / 2;

                g.append('text')
                    .attr('x', x)
                    .attr('y', y)
                    .attr('text-anchor', 'middle')
                    .attr('dominant-baseline', 'middle')
                    .style('font-size', `${fontScale(word.value)}px`)
                    .style('font-weight', 'bold')
                    .style('fill', colorScale(i))
                    .style('cursor', 'pointer')
                    .text(word.text)
                    .on('mouseover', function() {
                        d3.select(this).style('opacity', 0.7);
                    })
                    .on('mouseout', function() {
                        d3.select(this).style('opacity', 1);
                    })
                    .append('title')
                    .text(`${word.text}: ${word.value}`);
            });
        }
    </script>
</body>
</html>
