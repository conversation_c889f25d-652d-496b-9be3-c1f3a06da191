<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>通用图表生成器 | D3 / ECharts / Chart.js</title>
  <style>
    body{font-family:system-ui,-apple-system,Segoe UI,Roboto,Helvetica,Arial;line-height:1.45;margin:0;background:#fafafa;color:#222}
    header{padding:12px 16px;border-bottom:1px solid #eee;background:#fff;position:sticky;top:0;z-index:10}
    header h1{font-size:18px;margin:0}
    main{display:grid;grid-template-columns:380px 1fr;gap:12px;padding:12px}
    .panel{background:#fff;border:1px solid #eee;border-radius:8px;overflow:hidden}
    .panel h2{font-size:14px;margin:0;padding:10px 12px;border-bottom:1px solid #eee;background:#fbfbfb}
    .panel .body{padding:10px 12px}
    textarea{width:100%;min-height:220px;font-family:ui-monospace,Consolas,monospace;font-size:12.5px}
    .row{display:flex;gap:8px;align-items:center;flex-wrap:wrap}
    select,input[type="text"],input[type="password"]{padding:6px 8px;border:1px solid #ddd;border-radius:6px;background:#fff}
    button{padding:8px 12px;border:1px solid #2f6feb;background:#2f6feb;color:#fff;border-radius:6px;cursor:pointer}
    button.secondary{background:#fff;color:#2f6feb}
    #chart{height:620px}
    .footer{font-size:12px;color:#666}
    details{margin-top:8px}
    code{background:#f3f3f3;padding:2px 4px;border-radius:4px}
    /* Enhanced UI */
    :root{--bg:#f7f9fc;--card:#ffffff;--text:#1f2328;--muted:#6e7781;--pri:#2f6feb;--pri-600:#1f5fe0;--ring:#e2e8f0}
    body{background:var(--bg);color:var(--text)}
    header{background:linear-gradient(135deg,#f7fbff,#eef3ff);border-bottom:1px solid var(--ring)}
    main{gap:16px}
    .panel{box-shadow:0 1px 2px rgba(0,0,0,.05);background:var(--card);border-color:var(--ring)}
    .panel h2{background:#f9fbff}
    textarea,select,input[type="text"],input[type="password"]{border-color:#d0d7de}

    button{background:var(--pri);border-color:var(--pri);}
    button:hover{background:var(--pri-600);border-color:var(--pri-600)}
    .btn-ghost{background:#fff;color:var(--pri);border-color:var(--pri)}
    .subtle{color:var(--muted);font-size:12px}
    .row.spc{justify-content:space-between}
    .input{display:flex;gap:8px;align-items:center}
    .input > label{font-size:12px;color:var(--muted)}
    .badge{display:inline-block;padding:2px 6px;border-radius:999px;background:#eef2ff;color:#243b80;font-size:12px;border:1px solid #d6defa}
    /* Overlay */
    #overlay{position:fixed;inset:0;display:none;align-items:center;justify-content:center;background:rgba(255,255,255,.6);backdrop-filter:saturate(1.2) blur(2px);z-index:99}
    .spinner{width:28px;height:28px;border:3px solid #c7d2fe;border-top-color:#4f46e5;border-radius:50%;animation:spin 1s linear infinite}
    @keyframes spin{to{transform:rotate(360deg)}}
  </style>
  <!-- Libraries -->
  <script src="https://cdn.jsdelivr.net/npm/echarts@5/dist/echarts.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/d3@7"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
</head>
<body>
  <header>
    <h1>通用图表生成器（支持自动判断 / 指定类型；D3、ECharts、Chart.js）</h1>
  </header>
  <main>
    <section class="panel">
      <h2>输入数据与设置</h2>
      <div class="body">
        <div class="row" style="margin-bottom:8px">
          <label>图表类型：</label>
          <select id="type">
            <option value="auto">自动判断</option>
            <optgroup label="基础">
              <option>bar</option><option>line</option><option>pie</option><option>scatter</option>
              <option>radar</option><option>boxplot</option><option>bubble</option><option>heatmap</option>
            </optgroup>
            <optgroup label="时间">
              <option value="calendar-heatmap">calendar-heatmap</option><option>gantt</option>
            </optgroup>
            <optgroup label="网络/层次">
              <option value="force">force</option><option value="sankey">sankey</option><option value="tree">dendrogram</option>
              <option value="chord">chord</option><option value="voronoi">voronoi</option>
            </optgroup>
            <optgroup label="地理">
              <option value="choropleth">choropleth</option><option value="map-scatter">map-scatter</option><option value="map-lines">map-lines</option>

            </optgroup>
          </select>
          <label>框架：</label>
          <select id="framework">
            <option value="auto">自动</option>
            <option value="echarts">ECharts</option>
            <option value="d3">D3.js</option>
            <option value="chartjs">Chart.js</option>
          </select>
          <input id="title" type="text" placeholder="标题（可选）" style="flex:1" />
        <div class="row spc" style="padding:4px 0 6px 0">
          <span class="badge">AI 辅助 · OpenRouter</span>
        </div>
        <div class="row" style="gap:12px;margin-bottom:6px">
          <div class="input"><label>API URL</label><input id="apiUrl" type="text" style="width:260px" placeholder="https://openrouter.ai/api/v1/chat/completions" /></div>
          <div class="input"><label>API Key</label><input id="apiKey" type="password" style="width:260px" placeholder="sk-or-..." /></div>
          <div class="input"><label>模型</label><input id="model" type="text" style="width:260px" placeholder="deepseek/deepseek-chat-v3-0324:free" /></div>
        </div>
        <div class="row" style="gap:8px;margin-bottom:6px">
          <button id="aiBtn">AI分析生成</button>
          <label class="input"><input id="autoRender" type="checkbox" checked /> 自动渲染</label>
          <button class="btn-ghost" id="saveCfgBtn" type="button">保存配置</button>
        </div>
        </div>
        <div class="row subtle" style="margin:4px 0 6px 0">
          说明：直接输入“业务描述或对话”，点“AI分析生成”；或直接粘贴已格式化的 JSON/CSV 后点“生成图表”。
        </div>
        <textarea id="input" placeholder="在此粘贴数据。支持：
1) JSON（推荐复杂图）
2) 简单CSV行（label,value 或 x,y[,size]）
可在文本中写：type: 柱状图/折线图/饼图/散点图/等值线图/点分布图/连线地图/力导向/桑基/树状图/雷达/箱线图/气泡图/热力图/日历热力图/甘特/弦图/Voronoi；framework: echarts|d3|chartjs
"></textarea>
        <div class="row" style="margin-top:8px">
          <button id="renderBtn">生成图表</button>
          <button class="secondary" id="sampleBtn">填充示例</button>
        </div>
        <details>
          <summary>输入格式示例</summary>
          <div class="footer">
            <p><b>基础（CSV）：</b><br>
              A,120\nB,200\nC,150</p>
            <p><b>散点/气泡（CSV）：</b><br>
              10,20,15\n30,40,8\n50,28,20</p>
            <p><b>日历热力图（CSV，YYYY-MM-DD,value）：</b><br>
              2025-01-01,10\n2025-01-02,20</p>
            <p><b>桑基（JSON）：</b></p>
            <pre style="white-space:pre-wrap">{
  "type": "sankey",
  "nodes": [{"name":"油气田"},{"name":"管道"},{"name":"炼化"}],
  "links": [{"source":"油气田","target":"管道","value":120},{"source":"管道","target":"炼化","value":90}]
}</pre>
            <p><b>地图等值面（JSON，需要 geojsonUrl）：</b></p>
            <pre style="white-space:pre-wrap">{
  "type":"choropleth",
  "geojsonUrl":"https://geo.datav.aliyun.com/areas_v3/bound/geojson?code=100000",
  "data":[{"name":"四川省","value":120},{"name":"北京市","value":80}]
}</pre>
          </div>
        </details>
      </div>
    </section>
    <section class="panel">
      <h2 id="panelTitle">预览</h2>
      <div class="body">
        <div id="chart" class="panel"></div>
        <canvas id="chartjsCanvas" style="display:none;height:620px;width:100%"></canvas>
      </div>
    </section>

      <div id="overlay" aria-hidden="true" style="display:none"><div class="spinner"></div></div>
  </main>

  <script>
    const $ = (s)=>document.querySelector(s);

    const typeSel = $('#type');
    const fwSel = $('#framework');
    const inputEl = $('#input');
    const titleEl = $('#title');
    const panelTitle = $('#panelTitle');
    const chartDiv = document.getElementById('chart');
    const chartjsCanvas = document.getElementById('chartjsCanvas');
    let echartsInst = null, chartjsInst = null;

    const zh2type = (t)=>{
      if(!t) return null; t = String(t).toLowerCase();
      const map = new Map([
        ['柱状图','bar'],['bar','bar'],['折线图','line'],['line','line'],['饼图','pie'],['环形图','pie'],['pie','pie'],
        ['散点图','scatter'],['scatter','scatter'],['气泡图','bubble'],['bubble','bubble'],['雷达图','radar'],['radar','radar'],
        ['箱线图','boxplot'],['box plot','boxplot'],['热力图','heatmap'],['heatmap','heatmap'],['日历热力图','calendar-heatmap'],['calendar heatmap','calendar-heatmap'],
        ['甘特图','gantt'],['gantt','gantt'],
        ['力导向图','force'],['force','force'],['桑基图','sankey'],['sankey','sankey'],['树状图','tree'],['dendrogram','tree'],['tree','tree'],
        ['弦图','chord'],['chord','chord'],['voronoi','voronoi'],
        ['等值线图','choropleth'],['choropleth','choropleth'],['点分布图','map-scatter'],['连线地图','map-lines']
      ]);
      for (const [k,v] of map) if (t.includes(k)) return v; return null;
    };

    const zh2fw = (s)=>{
      if(!s) return null; s=String(s).toLowerCase();
      if(s.includes('echarts')) return 'echarts';
      if(s.includes('chart.js')||s.includes('chartjs')||s.includes('chart js')) return 'chartjs';
      if(s.includes('d3')) return 'd3';
      return null;
    };

    function detectFromFreeText(txt){
      const typeMatch = txt.match(/type\s*[:：]\s*([^\n]+)/i) || txt.match(/图表\s*[:：]\s*([^\n]+)/i);
      const fwMatch = txt.match(/framework\s*[:：]\s*([^\n]+)/i) || txt.match(/框架\s*[:：]\s*([^\n]+)/i);
      const type = zh2type(typeMatch?typeMatch[1].trim():null) || zh2type(txt);
      const fw = zh2fw(fwMatch?fwMatch[1].trim():null);
      return {type, framework: fw};
    }

    function tryParseJSON(txt){ try { return JSON.parse(txt); } catch(e){ return null; } }

    function parseCSVLines(txt){
      return txt.split(/\r?\n/).map(s=>s.trim()).filter(Boolean).map(line=>line.split(/\s*,\s*/));
    }

    function normalizeInput(txt){
      const json = tryParseJSON(txt);
      if(json) return {kind:'json', data:json};
      const csv = parseCSVLines(txt);
      return {kind:'csv', data:csv};
    }

    function autoTypeByData(norm){
      if(norm.kind==='json'){
        const d=norm.data; if(d.type) return zh2type(d.type)||d.type;
        if(d.nodes&&d.links&&Array.isArray(d.nodes)&&Array.isArray(d.links)) return 'force';
        if(d.matrix) return 'chord';
        if(d.geojsonUrl) return 'choropleth';
        if(d.tasks) return 'gantt';
      } else if(norm.kind==='csv'){
        const n = norm.data[0]?.length||0;
        if(n===2) return 'bar';
        if(n===3) return 'scatter';
      }
      return 'bar';
    }

    function clearCanvases(){
      chartDiv.style.display='block'; chartjsCanvas.style.display='none';
      if(echartsInst){echartsInst.dispose(); echartsInst=null;}
      if(chartjsInst){chartjsInst.destroy(); chartjsInst=null;}
      chartDiv.innerHTML='';
    }

    async function render(){
      clearCanvases();
      const raw = inputEl.value.trim(); if(!raw){ alert('请输入数据'); return; }
      const pickedType = typeSel.value;
      const pickedFw = fwSel.value;
      const meta = detectFromFreeText(raw);
      let norm = normalizeInput(raw);
      let type = (pickedType!=='auto'? pickedType : (meta.type||autoTypeByData(norm)));
      let fw = (pickedFw!=='auto'? pickedFw : (meta.framework||chooseFramework(type)));
      panelTitle.textContent = `${titleEl.value||'预览'} | ${type} @ ${fw}`;

      try{
        if(fw==='echarts') await renderECharts(type, norm);
        else if(fw==='chartjs') await renderChartJS(type, norm);
        else await renderD3(type, norm);
      }catch(err){
        console.error(err);
        chartDiv.innerHTML = `<div style="padding:12px;color:#b00020">渲染失败：${err.message}</div>`;
      }
    }

    function chooseFramework(type){
      const d3Prefer = new Set(['chord','voronoi','gantt']);
      if(d3Prefer.has(type)) return 'd3';
      return 'echarts';
    }

    // ---------- ECharts Renderers ----------
    async function renderECharts(type, norm){
      echartsInst = echarts.init(chartDiv);
      const set = (opt)=>echartsInst.setOption(opt, true);
      if(type==='bar' || type==='line' || type==='pie'){
        const {labels, values} = toLabelsValues(norm);
        if(type==='pie'){
          set({tooltip:{}, series:[{type:'pie',radius:['30%','70%'],data:labels.map((l,i)=>({name:l,value:values[i]}))}] ,legend:{}});
        } else {
          set({tooltip:{},xAxis:{type:'category',data:labels},yAxis:{},series:[{type:type,data:values}]});
        }
      } else if(type==='scatter' || type==='bubble'){
        const points = toPoints(norm); // [x,y,(size)]
        set({tooltip:{},xAxis:{},yAxis:{},series:[{type:'scatter',symbolSize:(d)=> type==='bubble'?(d[2]||10):8, data:points}]});
      } else if(type==='radar'){
        const j = requireJSON(norm, 'radar 需要 JSON，如 {indicators:[{name,max}], series:[{name, value:[]}]}');
        set({tooltip:{},radar:{indicator:j.indicators||[]},series:[{type:'radar',data:j.series||[]}]});
      } else if(type==='boxplot'){
        const j = requireJSON(norm, 'boxplot 需要 JSON，如 {categories:[], data:[[min,Q1,median,Q3,max],...]}');
        set({xAxis:{type:'category',data:j.categories||[]},yAxis:{},tooltip:{},series:[{type:'boxplot',data:j.data||[]}]});
      } else if(type==='heatmap'){
        const j = (norm.kind==='json')?norm.data:null;
        if(j && j.grid){
          set({tooltip:{},xAxis:{type:'category',data:j.grid.x},yAxis:{type:'category',data:j.grid.y},visualMap:{min:j.min||0,max:j.max||100, calculable:true, orient:'horizontal',left:'center'},series:[{type:'heatmap',data:j.data}]});
        } else {
          // CSV x,y,val
          const data = norm.data.map(r=>[+r[0],+r[1],+r[2]||0]);
          const xs=[...new Set(data.map(d=>d[0]))].sort((a,b)=>a-b);
          const ys=[...new Set(data.map(d=>d[1]))].sort((a,b)=>a-b);
          const m=data.map(([x,y,v])=>[xs.indexOf(x),ys.indexOf(y),v]);
          set({tooltip:{},xAxis:{type:'category',data:xs},yAxis:{type:'category',data:ys},visualMap:{min:0,max:Math.max(...data.map(d=>d[2]||0)),calculable:true,orient:'horizontal',left:'center'},series:[{type:'heatmap',data:m}]});
        }
      } else if(type==='calendar-heatmap'){
        const arr = norm.kind==='csv'? norm.data.map(r=>[r[0], +r[1]]): (norm.data.data||[]);
        const year = (arr[0]?.[0]||'2025').slice(0,4);
        set({calendar:{range:year}, visualMap:{min:0,max:Math.max(1,...arr.map(a=>a[1])), calculable:true, orient:'horizontal',left:'center'}, series:[{type:'heatmap',coordinateSystem:'calendar',data:arr}]});
      } else if(type==='sankey'){
        const j=requireJSON(norm,'sankey 需要 JSON: {nodes:[{name}],links:[{source,target,value}]}');
        set({tooltip:{},series:[{type:'sankey',data:j.nodes,links:j.links,label:{show:true}}]});
      } else if(type==='force'){
        const j=requireJSON(norm,'force 需要 JSON: {nodes:[{name}...],links:[{source,target,value}]} 或 CSV 源,目标,值');
        let nodes=j.nodes,links=j.links;
        if(!nodes||!links){ // CSV fallback
          const edges = norm.data.map(r=>({source:r[0],target:r[1],value:+(r[2]||1)}));
          const names=[...new Set(edges.flatMap(e=>[e.source,e.target]))];
          nodes = names.map(n=>({name:n})); links = edges;
        }
        set({tooltip:{},series:[{type:'graph',layout:'force',roam:true,data:nodes,links:links,label:{show:true},force:{repulsion:120}}]});
      } else if(type==='tree'){
        const j=requireJSON(norm,'dendrogram 需要 JSON: {name:"root", children:[...]}');
        set({tooltip:{},series:[{type:'tree',data:[j],top:'5%',left:'8%',bottom:'5%',right:'20%',symbolSize:8,label:{position:'left',verticalAlign:'middle',align:'right'},leaves:{label:{position:'right',align:'left'}},expandAndCollapse:true,initialTreeDepth:3}]});
      } else if(type==='choropleth'){
        const j=requireJSON(norm,'choropleth 需要 JSON: {geojsonUrl:"...", data:[{name,value}]}');
        const geo = await fetch(j.geojsonUrl).then(r=>r.json());
        echarts.registerMap('customMap', geo);
        set({tooltip:{},visualMap:{min:0,max:Math.max(1,...(j.data||[]).map(d=>d.value||0)),left:'left',top:'bottom',calculable:true},series:[{type:'map',map:'customMap',data:j.data||[],emphasis:{label:{show:true}}}]});
      } else if(type==='map-scatter' || type==='map-lines'){
        const j=requireJSON(norm,'map 需要 JSON: {geojsonUrl:"...", points?:[{name, coord:[lng,lat], value}], lines?:[{coords:[[lng,lat],[lng,lat]], value}]}');
        const geo = await fetch(j.geojsonUrl).then(r=>r.json());
        echarts.registerMap('customMap', geo);
        const series=[];
        if(type==='map-scatter' && j.points){ series.push({type:'scatter',coordinateSystem:'geo',data:j.points.map(p=>({name:p.name, value:[...p.coord, p.value||1]})),symbolSize:(v)=>6+Math.sqrt(v[2]||1)}); }
        if(type==='map-lines' && j.lines){ series.push({type:'lines',coordinateSystem:'geo',data:j.lines,polyline:false,effect:{show:true,trailLength:0.2,symbol:'arrow',symbolSize:6},lineStyle:{width:1,opacity:0.6}}); }
        set({tooltip:{},geo:{map:'customMap', roam:true}, series});
      } else {
        throw new Error('该类型在 ECharts 渲染器中暂不支持，请切换到 D3 或 Chart.js');
      }
    }

    function toLabelsValues(norm){
      if(norm.kind==='json'){
        const j=norm.data; if(j.labels&&j.values) return {labels:j.labels, values:j.values};
        if(Array.isArray(j.data)){
          if(typeof j.data[0]==='object') return {labels:j.data.map(d=>d.name||d.label), values:j.data.map(d=>d.value)};
          if(Array.isArray(j.data[0])) return {labels:j.data.map(d=>d[0]), values:j.data.map(d=>+d[1])};
        }
      }
      const rows = norm.data; return {labels:rows.map(r=>r[0]), values:rows.map(r=>+r[1])};
    }

    function toPoints(norm){
      if(norm.kind==='json' && Array.isArray(norm.data.data)) return norm.data.data;
      return norm.data.map(r=>[+r[0],+r[1], r[2]?+r[2]:undefined]);
    }

    function requireJSON(norm, msg){ if(norm.kind!=='json') throw new Error(msg); return norm.data; }

    // ---------- Chart.js (basic only) ----------
    async function renderChartJS(type, norm){
      const ctx = chartjsCanvas.getContext('2d'); chartDiv.style.display='none'; chartjsCanvas.style.display='block';
      if(type==='bar'||type==='line'||type==='pie'||type==='scatter'){
        const {labels, values} = toLabelsValues(norm);
        const cfg={type:(type==='scatter'?'scatter':type),data:{labels:(type==='scatter'?undefined:labels),datasets:[{label:titleEl.value||type,data:(type==='scatter'?labels.map((l,i)=>({x:+l,y:values[i]})):values),backgroundColor:'rgba(47,111,235,0.4)',borderColor:'#2f6feb'}]},options:{responsive:true,plugins:{legend:{display:type==='pie'}}}};
        chartjsInst = new Chart(ctx, cfg);
      } else {
        throw new Error('Chart.js 仅演示基础图（bar/line/pie/scatter），其余请切换 ECharts 或 D3');
      }
    }

    // ---------- D3 Renderers (specialty) ----------
    async function renderD3(type, norm){
      const width = chartDiv.clientWidth, height = chartDiv.clientHeight;
      if(type==='chord'){
        const j=requireJSON(norm,'chord 需要 JSON: {matrix:[[..],[..],...], labels:[..]}');
        const svg=d3.select(chartDiv).append('svg').attr('width',width).attr('height',height);
        const outerRadius=Math.min(width,height)*0.45, innerRadius=outerRadius-20;
        const chord=d3.chord().padAngle(0.05)(j.matrix), arc=d3.arc().innerRadius(innerRadius).outerRadius(outerRadius);
        const ribbon=d3.ribbon().radius(innerRadius);
        const g=svg.append('g').attr('transform',`translate(${width/2},${height/2})`);
        const color=d3.scaleOrdinal(d3.schemeCategory10);
        g.append('g').selectAll('g').data(chord.groups).join('g')
          .append('path').attr('d',arc).attr('fill',d=>color(d.index)).append('title').text(d=>`${j.labels?.[d.index]||d.index}`);
        g.append('g').attr('fill-opacity',0.8).selectAll('path').data(chord).join('path')
          .attr('d',ribbon).attr('fill',d=>color(d.target.index)).attr('stroke','#fff');
      } else if(type==='voronoi'){
        const pts = toPoints(norm).map(p=>({x:+p[0],y:+p[1]}));
        const svg=d3.select(chartDiv).append('svg').attr('width',width).attr('height',height);
        const delaunay = d3.Delaunay.from(pts, d=>d.x, d=>d.y);
        const voronoi = delaunay.voronoi([0,0,width,height]);
        svg.append('g').selectAll('path').data(pts).join('path')
          .attr('d',(d,i)=>voronoi.renderCell(i)).attr('fill','#f0f6ff').attr('stroke','#90b4f7');
        svg.append('g').selectAll('circle').data(pts).join('circle').attr('cx',d=>d.x).attr('cy',d=>d.y).attr('r',3).attr('fill','#2f6feb');
      } else if(type==='gantt'){
        const j=requireJSON(norm,'gantt 需要 JSON: {tasks:[{name,start,end,status?}]}，日期格式 YYYY-MM-DD 或 ISO');
        const tasks=j.tasks||[]; const parse=(v)=>new Date(v);
        const s=d3.min(tasks,t=>parse(t.start)), e=d3.max(tasks,t=>parse(t.end));
        const svg=d3.select(chartDiv).append('svg').attr('width',width).attr('height',height);
        const x=d3.scaleTime().domain([s,e]).range([120,width-20]);
        const y=d3.scaleBand().domain(tasks.map(t=>t.name)).range([20,height-40]).padding(0.2);
        const color=d3.scaleOrdinal().domain(['planned','ongoing','done']).range(['#90caf9','#42a5f5','#2e7d32']);
        svg.append('g').attr('transform',`translate(0,${height-40})`).call(d3.axisBottom(x));

    // --- AI 调用：OpenRouter ---
    const overlay = document.getElementById('overlay');
    const apiUrlEl = document.getElementById('apiUrl');
    const apiKeyEl = document.getElementById('apiKey');
    const modelEl = document.getElementById('model');
    const autoRenderEl = document.getElementById('autoRender');

    (function loadCfg(){
      const cfg = JSON.parse(localStorage.getItem('aiChartCfg')||'{}');
      if(cfg.apiUrl) apiUrlEl.value = cfg.apiUrl; else apiUrlEl.value='https://openrouter.ai/api/v1/chat/completions';
      if(cfg.apiKey) apiKeyEl.value = cfg.apiKey;
      if(cfg.model) modelEl.value = cfg.model; else modelEl.value='deepseek/deepseek-chat-v3-0324:free';
      if(typeof cfg.autoRender === 'boolean') autoRenderEl.checked = cfg.autoRender;
    })();

    document.getElementById('saveCfgBtn').addEventListener('click', ()=>{
      const cfg = { apiUrl: apiUrlEl.value.trim(), apiKey: apiKeyEl.value.trim(), model: modelEl.value.trim(), autoRender: autoRenderEl.checked };
      localStorage.setItem('aiChartCfg', JSON.stringify(cfg));
      alert('配置已保存到本地浏览器');
    });

    document.getElementById('aiBtn').addEventListener('click', async ()=>{
      const apiUrl = apiUrlEl.value.trim();
      const apiKey = apiKeyEl.value.trim();
      const model = modelEl.value.trim();
      if(!apiUrl || !apiKey || !model){ alert('请填写 API URL、API Key 与 模型'); return; }
      const userPrompt = inputEl.value.trim();
      if(!userPrompt){ alert('请在输入框描述你的图表需求或粘贴原始数据'); return; }

      const sys = `你是数据可视化助手。请将用户的描述或原始文本，转为严格的 JSON（仅输出 JSON），用于直接驱动图表渲染。\n支持的type: bar,line,pie,scatter,bubble,radar,boxplot,heatmap,calendar-heatmap,gantt,force,sankey,tree,chord,voronoi,choropleth,map-scatter,map-lines。\n约定：\n- 总是输出 {type, framework?, data:...} 顶层结构。\n- 若是基础图，输出 {labels:[], values:[]} 或 {data:[{name,value}]}; \n- 散点/气泡：{data:[[x,y,size?],...]};\n- 雷达：{indicators:[{name,max}], series:[{name?, value:[]}...]};\n- 箱线图：{categories:[], data:[[min,Q1,median,Q3,max],...]};\n- 热力图：{grid:{x:[],y:[]}, data:[[xIdx,yIdx,value],...] 或 CSV 方案};\n- 日历热力图：{data:[[YYYY-MM-DD,value],...]};\n- 甘特：{tasks:[{name,start,end,status?}]};\n- 力导向：{nodes:[{name}...],links:[{source,target,value?}]};\n- 桑基：{nodes:[{name}],links:[{source,target,value}]};\n- 树：{name:\"root\", children:[...]};\n- 弦图：{labels:[], matrix:[[...],...]};\n- Voronoi：{data:[[x,y],...]};\n- 地图：choropleth {geojsonUrl:\"...\", data:[{name,value}]}; map-scatter {geojsonUrl:\"...\", points:[{name, coord:[lng,lat], value?}]}; map-lines {geojsonUrl:\"...\", lines:[{coords:[[lng,lat],[lng,lat]], value?}]}.\n- 可选 framework: echarts|d3|chartjs。若无则交由前端自动选择。\n- 确保所有字段和类型正确可用。`;

      try{
        overlay.style.display='flex';
        const resp = await fetch(apiUrl,{
          method:'POST',
          headers:{
            'Content-Type':'application/json',
            'Authorization': `Bearer ${apiKey}`,
            'HTTP-Referer': location.origin,
            'X-Title': 'AI Chart Generator'
          },
          body: JSON.stringify({
            model,
            messages:[
              {role:'system', content: sys},
              {role:'user', content: userPrompt}
            ],
            temperature: 0.2
          })
        });
        const data = await resp.json();
        const content = data?.choices?.[0]?.message?.content || '';
        const jsonText = (content.match(/```json\n([\s\S]*?)\n```/i)?.[1] || content).trim();
        let parsed;
        try { parsed = JSON.parse(jsonText); } catch(e){ throw new Error('模型输出不是合法 JSON:\n'+jsonText.slice(0,400)); }
        inputEl.value = JSON.stringify(parsed, null, 2);
        if(autoRenderEl.checked){ await render(); }
      } catch(err){
        alert('AI 调用失败：'+ err.message);
        console.error(err);
      } finally {
        overlay.style.display='none';
      }
    });

        svg.append('g').attr('transform','translate(120,0)').call(d3.axisLeft(y));
        svg.append('g').selectAll('rect').data(tasks).join('rect')
          .attr('x',d=>x(parse(d.start))).attr('y',d=>y(d.name)).attr('width',d=>Math.max(2,x(parse(d.end))-x(parse(d.start)))).attr('height',y.bandwidth())
          .attr('fill',d=>color(d.status||'ongoing')).append('title').text(d=>`${d.name}: ${d.start} -> ${d.end}`);
      } else {
        // Fallback to ECharts for other types if possible
        return renderECharts(type, norm);
      }
    }

    document.getElementById('renderBtn').addEventListener('click', render);
    document.getElementById('sampleBtn').addEventListener('click', ()=>{
      typeSel.value='auto'; fwSel.value='auto'; titleEl.value='示例：油气产量';
      inputEl.value = 'type: 柱状图\nA,120\nB,200\nC,150\nD,80';
    });



    // --- AI 调用：OpenRouter ---
    const overlay = document.getElementById('overlay');
    const apiUrlEl = document.getElementById('apiUrl');
    const apiKeyEl = document.getElementById('apiKey');
    const modelEl = document.getElementById('model');
    const autoRenderEl = document.getElementById('autoRender');

    (function loadCfg(){
      const cfg = JSON.parse(localStorage.getItem('aiChartCfg')||'{}');
      if(cfg.apiUrl) apiUrlEl.value = cfg.apiUrl; else apiUrlEl.value='https://openrouter.ai/api/v1/chat/completions';
      if(cfg.apiKey) apiKeyEl.value = cfg.apiKey;
      if(cfg.model) modelEl.value = cfg.model; else modelEl.value='deepseek/deepseek-chat-v3-0324:free';
      if(typeof cfg.autoRender === 'boolean') autoRenderEl.checked = cfg.autoRender;
    })();

    document.getElementById('saveCfgBtn').addEventListener('click', ()=>{
      const cfg = { apiUrl: apiUrlEl.value.trim(), apiKey: apiKeyEl.value.trim(), model: modelEl.value.trim(), autoRender: autoRenderEl.checked };
      localStorage.setItem('aiChartCfg', JSON.stringify(cfg));
      alert('配置已保存到本地浏览器');
    });

    document.getElementById('aiBtn').addEventListener('click', async ()=>{
      const apiUrl = apiUrlEl.value.trim();
      const apiKey = apiKeyEl.value.trim();
      const model = modelEl.value.trim();
      if(!apiUrl || !apiKey || !model){ alert('请填写 API URL、API Key 与 模型'); return; }
      const userPrompt = inputEl.value.trim();
      if(!userPrompt){ alert('请在输入框描述你的图表需求或粘贴原始数据'); return; }

      const sys = `你是数据可视化助手。请将用户的描述或原始文本，转为严格的 JSON（仅输出 JSON），用于直接驱动图表渲染。\n支持的type: bar,line,pie,scatter,bubble,radar,boxplot,heatmap,calendar-heatmap,gantt,force,sankey,tree,chord,voronoi,choropleth,map-scatter,map-lines。\n约定：\n- 总是输出 {type, framework?, data:...} 顶层结构。\n- 若是基础图，输出 {labels:[], values:[]} 或 {data:[{name,value}]}; \n- 散点/气泡：{data:[[x,y,size?],...]};\n- 雷达：{indicators:[{name,max}], series:[{name?, value:[]}...]};\n- 箱线图：{categories:[], data:[[min,Q1,median,Q3,max],...]};\n- 热力图：{grid:{x:[],y:[]}, data:[[xIdx,yIdx,value],...] 或 CSV 方案};\n- 日历热力图：{data:[[YYYY-MM-DD,value],...]};\n- 甘特：{tasks:[{name,start,end,status?}]};\n- 力导向：{nodes:[{name}...],links:[{source,target,value?}]};\n- 桑基：{nodes:[{name}],links:[{source,target,value}]};\n- 树：{name:\"root\", children:[...]};\n- 弦图：{labels:[], matrix:[[...],...]};\n- Voronoi：{data:[[x,y],...]};\n- 地图：choropleth {geojsonUrl:\"...\", data:[{name,value}]}; map-scatter {geojsonUrl:\"...\", points:[{name, coord:[lng,lat], value?}]}; map-lines {geojsonUrl:\"...\", lines:[{coords:[[lng,lat],[lng,lat]], value?}]}.\n- 可选 framework: echarts|d3|chartjs。若无则交由前端自动选择。\n- 确保所有字段和类型正确可用。`;

      try{
        overlay.style.display='flex';
        const resp = await fetch(apiUrl,{
          method:'POST',
          headers:{
            'Content-Type':'application/json',
            'Authorization': `Bearer ${apiKey}`,
            'HTTP-Referer': location.origin,
            'X-Title': 'AI Chart Generator'
          },
          body: JSON.stringify({
            model,
            messages:[
              {role:'system', content: sys},
              {role:'user', content: userPrompt}
            ],
            temperature: 0.2
          })
        });
        const data = await resp.json();
        const content = data?.choices?.[0]?.message?.content || '';
        const jsonText = (content.match(/```json\n([\s\S]*?)\n```/i)?.[1] || content).trim();
        let parsed;
        try { parsed = JSON.parse(jsonText); } catch(e){ throw new Error('模型输出不是合法 JSON:\n'+jsonText.slice(0,400)); }
        inputEl.value = JSON.stringify(parsed, null, 2);
        if(autoRenderEl.checked){ await render(); }
      } catch(err){
        alert('AI 调用失败：'+ err.message);
        console.error(err);
      } finally {
        overlay.style.display='none';
      }
    });

