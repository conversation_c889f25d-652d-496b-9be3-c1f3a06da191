我需要你帮助我设计一个专门用于图表生成的网站，以下是四个主流可视化工具支持的图表类型及其主要应用场景的详细分类，我希望你设计一个独立html文件构成的网页，可以将用户输入的文字进行图表化显示，用户可以明确的指出使用柱状图、折线图、饼图、散点图、等值线图（Choropleth）、点分布图、连线地图、力导向图（Force-Directed Graph）、桑基图（Sankey）、树状图（Dendrogram）、雷达图、箱线图（Box Plot）、气泡图、热力图、日历热力图、甘特图、弦图（Chord Diagram）、Voronoi图中的哪一种图表类型，然后使用对应的框架进行绘制。如果用户不指定具体的类型，请你根据用户输入的内容自动判断使用哪种图表类型进行绘制。



我希望你使用如下框架进行绘制：

1. D3.js
特点：底层灵活，适合高度定制化场景

支持图表类型：

基础图表：柱状图、折线图、饼图、散点图
地理可视化：等值线图（Choropleth）、点分布图、连线地图
网络关系：力导向图（Force-Directed Graph）、桑基图（Sankey）、树状图（Dendrogram）
高级图表：雷达图、箱线图（Box Plot）、气泡图、热力图
时间序列：日历热力图、甘特图
其他：弦图（Chord Diagram）、Voronoi图
应用场景：

学术研究（复杂数据关系展示）
金融领域（交互式K线图）
社交网络分析（关系图谱）
定制化企业仪表盘
2. ECharts
特点：企业级应用，支持动态数据与3D

支持图表类型：

常规图表：折线图、柱状图（含堆叠/瀑布）、饼图（环形图、玫瑰图）
地理相关：3D地图、GIS路径可视化、热力图
多维数据：平行坐标图、雷达图、散点矩阵
关系型：关系图（Graph）、桑基图、树图
特殊图表：漏斗图、仪表盘、象形图（Pictorial Bar）
动态效果：时间轴动画、数据区域缩放
应用场景：

商业智能（BI系统动态报表）
实时监控（如疫情地图）
电商（用户行为漏斗分析）
3D场景展示（如城市建筑分布）
3. Chart.js (v3.9.1)
特点：轻量简洁，适合快速开发

支持图表类型：

基础8大类型：
柱状图（Bar）
折线图（Line）
饼图/环形图（Doughnut）
雷达图（Radar）
散点图/气泡图（Scatter/Bubble）
面积图（Area）
极地图（Polar Area）
应用场景：

移动端页面（响应式设计）
小型项目快速原型开发
教育领域（简单数据教学）
4. draw.io (Diagrams.net)
特点：非编程图表工具，侧重架构与流程

支持图表类型：

流程图（Flowchart）
UML图（用例图、类图）
架构图（AWS/GCP云架构）
工程图（电路图、网络拓扑）
组织架构图
看板/Kanban
应用场景：

软件设计（系统架构文档）
项目管理（流程设计）
教学演示（算法流程图）